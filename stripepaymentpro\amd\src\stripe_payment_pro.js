// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (optionally) any later version.
//
// <PERSON><PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe Payment Pro JavaScript module for Moodle enrolment plugin
 *
 * This module handles Stripe payment processing, coupon functionality,
 * and embedded checkout integration for the stripepaymentpro enrolment plugin.
 *
 * @module     enrol_stripepaymentpro/stripe_payment_pro
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

import ajax from 'core/ajax';

const { call: fetchMany } = ajax;

// Global variable to store the active Stripe Embedded Checkout instance
// This is crucial for managing single active checkout.
window.activeCheckoutInstance = null;

// Repository functions for payment flow (keep these for payment functionality)
const applyCoupon = (couponinput, instanceid) =>
    fetchMany([{ methodname: "moodle_stripepaymentpro_applycoupon", args: { couponinput, instanceid } }])[0];

const stripeenrol = (userid, couponid, instanceid) =>
    fetchMany([{ methodname: "moodle_stripepaymentpro_stripe_enrol", args: { userid, couponid, instanceid } }])[0];

const createDOM = (instanceid) => {
    const cache = new Map();
    return {
        getelement(id) {
            const fullid = `${id}-${instanceid}`;
            if (!cache.has(fullid)) {
                cache.set(fullid, document.getElementById(fullid));
            }
            return cache.get(fullid);
        },
        setelement(id, html) {
            const element = this.getelement(id);
            if (element) {
                element.innerHTML = html;
            }
        },
        toggleelement(id, show) {
            const element = this.getelement(id);
            if (element) {
                element.style.display = show ? "block" : "none";
            }
        },
        focuselement(id) {
            const element = this.getelement(id);
            if (element) {
                element.focus();
            }
        },
        setbutton(id, disabled, text, opacity = disabled ? "0.7" : "1") {
            const button = this.getelement(id);
            if (button) {
                button.disabled = disabled;
                button.textContent = text;
                button.style.opacity = opacity;
                button.style.cursor = disabled ? "not-allowed" : "pointer";
            }
        },
    };
};

function stripe_payment_pro(userid, couponid, instanceid, publishablekey, pleasewaitstring, entercoupon, couponappling, paymentgatewaytype, isFirstInstance) {
    const DOM = createDOM(instanceid);
    const stripe = window.Stripe(publishablekey);

    if (typeof window.Stripe === "undefined") {
        // Display an error if Stripe.js is not loaded
        displayMessage("paymentresponse", "Stripe.js library not loaded. Please check your template includes.", "error");
        return;
    }

    const displayMessage = (containerid, message, type) => {
        let color;
        switch (type) {
            case "error": color = "red"; break;
            case "success": color = "green"; break;
            case "info": color = "blue"; break;
            default: color = "blue"; break;
        }
        DOM.setelement(containerid, `<p style="color: ${color}; font-weight: bold;">${message}</p>`);
        DOM.toggleelement(containerid, true);
    };

    const clearError = (containerId) => {
        DOM.setelement(containerId, "");
        DOM.toggleelement(containerId, false);
    };

    const updateUIFromServerResponse = (data) => {
        if (data.message) {
            displayMessage("showmessage", data.message, data.uistate === "error" ? "error" : "success");
        } else {
            clearError("showmessage");
        }

        // Show total section when payment is required (paid state means free enrollment)
        DOM.toggleelement("total", data.uistate === "paid" || data.uistate === "discount");

        if (data.uistate !== "error") {
            DOM.toggleelement("discountsection", data.showsections.discountsection);

            if (data.showsections.discountsection) {
                if (data.couponname) {
                    DOM.setelement("discounttag", data.couponname);
                }
                if (data.discountamount) {
                    // Use currency symbol ($ for USD) instead of currency code
                    const currencySymbol = data.currency === 'USD' ? '$' : data.currency + ' ';
                    DOM.setelement("discountamountdisplay", `-${currencySymbol}${data.discountamount}`);
                }
                if (data.discountamount && data.discountvalue) {
                    const currencySymbol = data.currency === 'USD' ? '$' : data.currency + ' ';
                    let note = data.coupontype === "percentoff"
                        ? `${data.discountvalue}% off`
                        : `${currencySymbol}${data.discountvalue} off`;

                    // Add duration information if available
                    if (data.couponduration) {
                        if (data.couponduration === "repeating" && data.coupondurationmonths) {
                            note += ` Expires in ${data.coupondurationmonths} months`;
                        } else if (data.couponduration !== "once") {
                            note += ` ${data.couponduration}`;
                        }
                    }

                    DOM.setelement("discountnote", note);
                }
            }

            if (data.status && data.currency) {
                const currencySymbol = data.currency === 'USD' ? '$' : data.currency + ' ';
                const formattedAmount = `${currencySymbol}${parseFloat(data.status).toFixed(2)}`;

                // Update main price display
                const mainprice = DOM.getelement("mainprice");
                if (mainprice) {
                    mainprice.textContent = formattedAmount;
                }

                // Update total amount display
                const totalamount = DOM.getelement("totalamount");
                if (totalamount) {
                    totalamount.textContent = formattedAmount;
                }

                // Update recurring price display if discount applies to recurring payments
                if (data.discounted_renewal_fee !== undefined && data.original_renewal_fee !== undefined) {
                    const discountedRenewalFee = parseFloat(data.discounted_renewal_fee);
                    const originalRenewalFee = parseFloat(data.original_renewal_fee);

                    if (discountedRenewalFee !== originalRenewalFee) {
                        // Update specific recurring price elements
                        const recurringPriceElements = [
                            DOM.getelement(`recurring-price-heading`),
                            DOM.getelement(`recurring-price-breakdown`),
                        ];

                        recurringPriceElements.forEach(element => {
                            if (element && element.textContent.includes(currencySymbol + originalRenewalFee.toFixed(2))) {
                                element.textContent = element.textContent.replace(
                                    currencySymbol + originalRenewalFee.toFixed(2),
                                    currencySymbol + discountedRenewalFee.toFixed(2)
                                );
                            }
                        });

                        // Also update any elements that contain "Then" text pattern
                        const allElements = document.querySelectorAll('*');
                        allElements.forEach(element => {
                            if (element.textContent && element.textContent.includes('Then ' + currencySymbol + originalRenewalFee.toFixed(2))) {
                                element.textContent = element.textContent.replace(
                                    'Then ' + currencySymbol + originalRenewalFee.toFixed(2),
                                    'Then ' + currencySymbol + discountedRenewalFee.toFixed(2)
                                );

                            }
                        });
                    }
                }
            }
        }
    };

    const applyCouponHandler = async (event) => {
        event.preventDefault();
        const couponinput = DOM.getelement("coupon");
        const couponcode = couponinput?.value.trim();
        if (!couponcode) {
            displayMessage("showmessage", entercoupon, "error");
            DOM.focuselement("coupon");
            return;
        }
        DOM.setbutton("apply", true, couponappling);
        try {
            const data = await applyCoupon(couponcode, instanceid);

            if (data?.status !== undefined) {
                couponid = couponcode;
                DOM.toggleelement("coupon", false);
                DOM.toggleelement("apply", false);
                updateUIFromServerResponse(data);
            } else {
                throw new Error("Invalid server response");
            }
        } catch (error) {
            console.error("Coupon application error:", error); // Debug log
            displayMessage("showmessage", error.message || "Coupon validation failed", "error");
            DOM.focuselement("coupon");
        } finally {
            DOM.setbutton("apply", false, "Apply"); // Ensure button is re-enabled
        }
    };

    const EnrollHandler = async () => {
        const enrollbutton = DOM.getelement("enrolbutton");
        if (!enrollbutton) return;
        clearError("paymentresponse");
        DOM.setbutton("enrolbutton", true, pleasewaitstring);

        try {
            // Handle Checkout mode (Elements mode uses embedded checkout which handles submission automatically)
            const paymentdata = await stripeenrol(userid, couponid, instanceid);
            if (paymentdata.error?.message) {
                displayMessage("paymentresponse", paymentdata.error.message, "error");
            } else if (paymentdata.status === "success" && paymentdata.redirecturl) {
                window.location.href = paymentdata.redirecturl;
            } else {
                displayMessage("paymentresponse", "Unknown error occurred during payment.", "error");
            }
        } catch (err) {
            displayMessage("paymentresponse", err.message, "error");
        } finally {
            DOM.toggleelement("enrolbutton", false);
        }
    };

    const setupEventListeners = () => {
        const elements = [
            { id: "apply", event: "click", handler: applyCouponHandler },
            { id: "enrolbutton", event: "click", handler: EnrollHandler },
        ];
        elements.forEach(({ id, event, handler }) => {
            const element = DOM.getelement(id);
            if (element) {
                element.addEventListener(event, handler);
            }
        });
        // Add event listener for the "Load Payment" button
        const loadPaymentButton = DOM.getelement("load-payment-button");
        if (loadPaymentButton) {
            loadPaymentButton.addEventListener('click', () => initializeElements());
        }
    };

    const initializeElements = async () => {
        const paymentelementContainer = DOM.getelement("payment-element");
        if (!paymentelementContainer) {
            displayMessage("paymentresponse", "Payment element container (ID: payment-element) not found in HTML. Check your template.", "error");
            return;
        }

        try {
            // Destroy any existing active checkout instance
            if (window.activeCheckoutInstance && window.activeCheckoutInstance.checkout) {

                window.activeCheckoutInstance.checkout.destroy();
                // Hide the container of the previously loaded payment element
                const prevContainerId = `payment-element-${window.activeCheckoutInstance.instanceid}`;
                const prevContainer = document.getElementById(prevContainerId);
                if (prevContainer) {
                    prevContainer.style.display = 'none';
                }
                // Show the "Load Payment" button for the previously active instance
                const prevLoadButton = document.getElementById(`load-payment-button-${window.activeCheckoutInstance.instanceid}`);
                if (prevLoadButton) {
                    prevLoadButton.style.display = 'block';
                }
            }

            // Fetch client secret for embedded checkout
            const fetchClientSecret = async () => {
                const response = await stripeenrol(userid, couponid, instanceid);



                if (response.error && response.error.message) {
                    throw new Error(response.error.message);
                }

                if (response.paymentintent) {

                    // Try to parse the paymentintent field
                    try {
                        const data = typeof response.paymentintent === 'string'
                            ? JSON.parse(response.paymentintent)
                            : response.paymentintent;

                        return data.client_secret;
                    } catch (parseError) {
                        console.error("Failed to parse paymentintent:", parseError);
                        console.error("PaymentIntent raw data:", response.paymentintent);
                        throw new Error("Invalid payment data received from server.");
                    }
                }

                throw new Error("No client secret found in response");
            };

            // Clear the container before mounting
            paymentelementContainer.innerHTML = '';

            // Initialize embedded checkout
            const checkout = await stripe.initEmbeddedCheckout({
                fetchClientSecret: fetchClientSecret,
            });

            const id = "payment-element";
            // Mount the embedded checkout
            checkout.mount(`#${id}-${instanceid}`);

            // Store the current checkout instance globally
            window.activeCheckoutInstance = { stripe, checkout, instanceid };

            // Show the payment element container and hide the "Load Payment" button
            DOM.toggleelement("payment-element", true);
            DOM.toggleelement("load-payment-button", false); // Hide the button that triggered the load

        } catch (err) {
            console.error("Stripe Elements initialization error:", err);
            displayMessage("paymentresponse", err.message || "Stripe initialization error. Check console.", "error");
            // If initialization fails, ensure the "Load Payment" button is visible again
            DOM.toggleelement("payment-element", false);
            DOM.toggleelement("load-payment-button", true);
        }
    };

    if (paymentgatewaytype === "elements") {
        if (isFirstInstance) {
            initializeElements();
        } else {
            // For non-first instances, ensure payment-element is hidden and load button is shown
            DOM.toggleelement("payment-element", false);
            DOM.toggleelement("load-payment-button", true);
        }
    }

    setupEventListeners();
}

/**
 * Initialize coupon settings for the coupon management page
 */
const initCouponSettings = () => {
    // Initialize Bootstrap dropdowns for three-dot buttons
    const dropdownButtons = document.querySelectorAll('[data-toggle="dropdown"]');
    dropdownButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Close all other dropdowns first
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                if (menu !== this.nextElementSibling) {
                    menu.classList.remove('show');
                }
            });

            // Toggle current dropdown
            const dropdownMenu = this.nextElementSibling;
            if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                dropdownMenu.classList.toggle('show');
            }
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    // Add search functionality only (deletion is handled by template functions)
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer && !document.querySelector('#coupon-search')) {
        const searchContainer = document.createElement('div');
        searchContainer.style.marginBottom = '15px';
        searchContainer.innerHTML = `
            <input type="text" id="coupon-search" placeholder="Search coupons..."
                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px;">
        `;
        tableContainer.parentNode.insertBefore(searchContainer, tableContainer);

        document.getElementById('coupon-search').addEventListener('input', (event) => {
            const searchTerm = event.target.value.toLowerCase();
            document.querySelectorAll('.table tbody tr').forEach(row => {
                row.style.display = row.textContent.toLowerCase().includes(searchTerm) ? '' : 'none';
            });
        });
    }
};

export default {
    stripe_payment_pro,
    initCouponSettings
};