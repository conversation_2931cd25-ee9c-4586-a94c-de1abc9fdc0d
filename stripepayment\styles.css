    .stripepayment-paymentcontainer {
        background: transparent;
        padding: 20px 0;
        width: 100%;
        max-width: 400px;
        margin: 0 auto;
    }

    .stripepayment-currencybox {
        margin-bottom: 8px;
    }

    .stripepayment-currencyselected {
        display: inline-flex;
        align-items: center;
        border: 2px solid #ccc;
        padding: 6px 12px;
        border-radius: 6px;
        background: #f1f1f1;
        font-weight: bold;
    }

    .stripepayment-currencyselected img {
        width: 20px;
        height: 14px;
        margin-right: 6px;
    }

    .stripepayment-note {
        font-size: 12px;
        color: gray;
        margin-bottom: 20px;
    }

    .stripepayment-item, .stripepayment-subtotal, .stripepayment-discount, .stripepayment-total {
        display: flex;
        justify-content: space-between;
        margin-bottom: 6px;
    }

    .stripepayment-itemtitle {
        font-weight: bold;
    }

    .stripepayment-itemsubtitle, .stripepayment-discountnote {
        font-size: 13px;
        color: gray;
        margin-bottom: 12px;
    }

    .stripepayment-line {
        height: 1px;
        background: #eee;
        margin: 12px 0;
    }

    .stripepayment-tag {
        background: #f1f1f1;
        padding: 3px 8px;
        border-radius: 6px;
        font-size: 14px;
    }

    .stripepayment-discountamount {
        color:rgb(167, 167, 167);
    }

    .stripepayment-couponsection {
        margin: 18px 0 12px 0;
        padding: 15px 0 12px 0;
        border-top: 1px solid #eee;
    }

    .stripepayment-total {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #eee;
    }

    .stripepayment-coupon-input-group {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;
    }

    .stripepayment-couponsection input {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 14px;
        box-sizing: border-box;
    }

    .stripepayment-applybtn {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 4px;
        cursor: pointer;
        white-space: nowrap;
        min-width: 80px;
    }

    .stripepayment-applybtn:hover {
        background-color: #5a6268;
    }

    .stripepayment-couponmessage {
        font-size: 12px;
        margin-top: 5px;
    }

    .stripepayment-error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 8px 12px;
        border-radius: 4px;
        margin: 10px 0;
        font-size: 14px;
    }

    .stripepayment-pay-btn:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stripepayment-paymentcontainer {
            width: 100%;
            max-width: 100%;
            padding: 15px 10px;
        }

        .stripepayment-currencyselected {
            font-size: 14px;
            padding: 8px 12px;
        }

        .stripepayment-currencyselected img {
            width: 18px;
            height: 12px;
        }

        .stripepayment-itemtitle {
            font-size: 14px;
        }

        .stripepayment-coupon-input-group {
            gap: 6px;
        }

        .stripepayment-couponsection input {
            padding: 10px 12px;
            font-size: 16px; /* Prevents zoom on iOS */
        }

        .stripepayment-applybtn {
            padding: 10px 12px;
            font-size: 16px;
            min-width: 70px;
        }

        .stripepayment-pay-btn {
            padding: 14px 0;
            font-size: 18px;
        }
    }

    @media (max-width: 480px) {
        .stripepayment-paymentcontainer {
            padding: 10px 5px;
        }

        .stripepayment-currencyselected {
            font-size: 13px;
            padding: 6px 10px;
        }

        .stripepayment-item, .stripepayment-subtotal, .stripepayment-discount, .stripepayment-total {
            font-size: 14px;
        }

        .stripepayment-coupon-input-group {
            flex-direction: column;
            gap: 8px;
        }

        .stripepayment-applybtn {
            width: 100%;
            min-width: auto;
        }

        .stripepayment-couponsection {
            margin: 15px 0 10px 0;
            padding: 12px 0 10px 0;
        }
    }

    /* Interactive Elements */
    .stripepayment-applybtn:active {
        transform: translateY(1px);
        background-color: #495057;
    }

    .stripepayment-couponsection input:focus {
        outline: none;
        border-color: #0070f3;
        box-shadow: 0 0 0 2px rgba(0, 112, 243, 0.2);
    }

    /* Smooth transitions */
    .stripepayment-applybtn, .stripepayment-pay-btn {
        transition: all 0.2s ease;
    }

    .stripepayment-couponsection input {
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    /* Payment button styling */
    .stripepayment-pay-btn {
        margin-top: 18px;
        width: 100%;
        background-color: #0070f3;
        color: white;
        border: none;
        padding: 12px 0;
        font-size: 16px;
        font-weight: bold;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .stripepayment-pay-btn:hover {
        background-color: #005fd1;
        filter: brightness(0.9);
    }

    .stripepayment-pay-btn:active {
        transform: translateY(1px);
        background-color: #004bb5;
        filter: brightness(0.8);
    }

    .stripepayment-pay-btn:disabled {
        background-color: #ccc;
        cursor: not-allowed;
        transform: none;
    }

    /* Utility classes */
    .stripepayment-hidden {
        display: none !important;
    }
