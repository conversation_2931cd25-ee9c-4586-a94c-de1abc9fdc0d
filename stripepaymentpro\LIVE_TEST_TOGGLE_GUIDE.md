# Stripe Payment Pro - Live/Test Mode Toggle Feature Guide

## 🎯 **Overview**

The Live/Test Mode Toggle feature allows you to seamlessly switch between Stripe's Test and Live environments without breaking your plugin functionality. This feature provides:

- **Safe Testing**: Test mode for development and testing with fake payments
- **Live Payments**: Live mode for real payment processing
- **Seamless Switching**: Toggle between modes without losing configuration
- **Automatic Migration**: Auto-migrates existing API keys to the new system
- **Price Recreation**: Automatically recreates prices when switching modes

## ✅ **Current Implementation Status**

### **✅ IMPLEMENTED:**
1. **Dual Key Storage System**: Separate storage for test and live API keys
2. **Mode Toggle Interface**: Settings page with clear mode selection
3. **Automatic Key Selection**: Code automatically uses correct keys based on mode
4. **Mode Change Detection**: Detects when mode changes and triggers price recreation
5. **API Key Validation**: Validates key format and completeness
6. **Legacy Compatibility**: Maintains backward compatibility with existing setups
7. **Visual Status Indicators**: Clear visual indicators of current mode

### **📁 Files Created:**
- `stripepayment/settings_copy.php` - Enhanced settings with Live/Test toggle
- `stripepaymentpro/lib_copy.php` - Enhanced lib with mode-aware API key management
- `stripepaymentpro/LIVE_TEST_TOGGLE_GUIDE.md` - This documentation

## 🚀 **How to Deploy**

### **Step 1: Backup Original Files**
```bash
# Backup original settings
cp stripepayment/settings.php stripepayment/settings_original_backup.php

# Backup original lib (already done in previous fixes)
# stripepaymentpro/lib_original_backup.php already exists
```

### **Step 2: Deploy Enhanced Files**
```bash
# Deploy enhanced settings
mv stripepayment/settings.php stripepayment/settings_old.php
mv stripepayment/settings_copy.php stripepayment/settings.php

# Deploy enhanced lib (if not already done)
mv stripepaymentpro/lib.php stripepaymentpro/lib_old.php
mv stripepaymentpro/lib_copy.php stripepaymentpro/lib.php
```

### **Step 3: Configure API Keys**
1. Go to **Site Administration → Plugins → Enrolments → Stripe Payment**
2. You'll see the new **Stripe Mode Settings** section
3. Configure your API keys in the appropriate sections

## 🔧 **How to Use the Live/Test Toggle**

### **Initial Setup**

1. **Navigate to Settings**:
   - Go to `Site Administration → Plugins → Enrolments → Stripe Payment`

2. **Configure Test Mode Keys**:
   - In the "Test Mode API Keys" section:
   - Enter your **Test Publishable Key** (starts with `pk_test_`)
   - Enter your **Test Secret Key** (starts with `sk_test_`)

3. **Configure Live Mode Keys**:
   - In the "Live Mode API Keys" section:
   - Enter your **Live Publishable Key** (starts with `pk_live_`)
   - Enter your **Live Secret Key** (starts with `sk_live_`)

4. **Select Current Mode**:
   - In the "Stripe Mode Settings" section:
   - Select either **Test Mode** or **Live Mode**
   - Save the settings

### **Switching Between Modes**

1. **To Switch to Test Mode**:
   - Change "Stripe Mode" dropdown to "Test Mode"
   - Save settings
   - Status will show: 🟢 **TEST MODE - Safe for testing**

2. **To Switch to Live Mode**:
   - Change "Stripe Mode" dropdown to "Live Mode"
   - Save settings
   - Status will show: 🔴 **LIVE MODE - Real payments will be processed**

### **Automatic Features**

- **Price Recreation**: When you switch modes, prices are automatically recreated in the new environment
- **Key Validation**: The system validates that your keys match the selected mode
- **Legacy Migration**: Existing API keys are automatically migrated to the new system

## 🔍 **Understanding the Interface**

### **Mode Status Indicators**
- 🟢 **TEST MODE - Safe for testing**: Using test API keys, no real payments
- 🔴 **LIVE MODE - Real payments will be processed**: Using live API keys, real payments
- ⚠️ **Configuration Error**: API keys are missing or invalid

### **API Key Sections**

1. **Stripe Mode Settings**:
   - Mode selection dropdown
   - Current status display

2. **Test Mode API Keys**:
   - Test publishable key (pk_test_...)
   - Test secret key (sk_test_...)

3. **Live Mode API Keys**:
   - Live publishable key (pk_live_...)
   - Live secret key (sk_live_...)

4. **Legacy API Keys (Deprecated)**:
   - Old single key fields (maintained for compatibility)

## ⚙️ **Technical Details**

### **New Configuration Keys**
```php
// Mode selection
get_config('enrol_stripepayment', 'stripe_mode') // 'test' or 'live'

// Test mode keys
get_config('enrol_stripepayment', 'test_publishablekey')
get_config('enrol_stripepayment', 'test_secretkey')

// Live mode keys
get_config('enrol_stripepayment', 'live_publishablekey')
get_config('enrol_stripepayment', 'live_secretkey')

// Legacy keys (still supported)
get_config('enrol_stripepayment', 'publishablekey')
$plugin->get_current_secret_key()
```

### **New Methods in lib_copy.php**
```php
// Get current mode
$plugin->get_stripe_mode() // Returns 'test' or 'live'

// Get appropriate API keys
$keys = $plugin->get_current_api_keys() // Returns array with keys and mode

// Get specific keys
$secret = $plugin->get_current_secret_key()
$publishable = $plugin->get_current_publishable_key()

// Validation and status
$validation = $plugin->validate_current_api_keys()
$status = $plugin->get_mode_status_display()

// Mode change detection
$changed = $plugin->has_mode_changed()
```

### **Automatic Migration Logic**
When you first access the new settings page:
1. If legacy keys exist and new keys are empty
2. System detects if legacy keys are test or live (by prefix)
3. Automatically migrates them to appropriate new key fields
4. Sets the mode accordingly

## 🧪 **Testing the Feature**

### **Test Mode Verification**
1. Set mode to "Test Mode"
2. Verify status shows green "TEST MODE"
3. Create a test enrolment instance
4. Attempt a test payment with Stripe test card numbers
5. Verify payment appears in Stripe Test Dashboard

### **Live Mode Verification**
1. Set mode to "Live Mode"
2. Verify status shows red "LIVE MODE"
3. **⚠️ CAUTION**: Only test with small amounts or refund immediately
4. Verify payments appear in Stripe Live Dashboard

### **Mode Switching Test**
1. Create enrolment instance in Test Mode
2. Switch to Live Mode
3. Verify prices are recreated automatically
4. Switch back to Test Mode
5. Verify functionality is maintained

## 🔒 **Security Considerations**

### **API Key Security**
- Live API keys should be treated as highly sensitive
- Test API keys are safer but should still be protected
- Keys are stored in Moodle's configuration system
- Consider using environment variables for additional security

### **Mode Switching Safety**
- Always verify the current mode before processing payments
- Test mode payments are safe and won't charge real money
- Live mode payments process real transactions
- The system provides clear visual indicators of current mode

## 🚨 **Important Warnings**

### **Live Mode Warnings**
- 🔴 **Live Mode processes real payments**
- Always test thoroughly in Test Mode first
- Verify all functionality before switching to Live Mode
- Monitor payments closely when first switching to Live Mode

### **Key Management**
- Never share or expose your API keys
- Regularly rotate your API keys for security
- Keep test and live keys clearly separated
- Backup your key configuration before making changes

## 🛠️ **Troubleshooting**

### **Common Issues**

1. **"Configuration Error" Status**:
   - Check that API keys are entered correctly
   - Verify key format (test keys start with pk_test_/sk_test_, live keys with pk_live_/sk_live_)
   - Ensure both publishable and secret keys are provided

2. **Prices Not Working After Mode Switch**:
   - Check debug logs for price recreation messages
   - Verify API keys are valid in the new mode
   - Try manually recreating enrolment instances if needed

3. **Legacy Keys Not Migrating**:
   - Check that legacy keys follow correct format
   - Manually copy keys to new fields if auto-migration fails
   - Clear cache after making changes

### **Debug Information**
Enable debug mode to see detailed information:
```php
// In config.php
$CFG->debug = DEBUG_DEVELOPER;
```

Look for debug messages like:
- "Mode changed from test to live"
- "Price recreation triggered"
- "API keys validation failed"
- "All prices found, no recreation needed"

## 📋 **Migration Checklist**

- [ ] Backup original files
- [ ] Deploy enhanced settings and lib files
- [ ] Configure test mode API keys
- [ ] Configure live mode API keys
- [ ] Test mode switching functionality
- [ ] Verify price recreation works
- [ ] Test payment processing in both modes
- [ ] Update documentation for your team
- [ ] Train administrators on new interface

## 🎉 **Benefits of the New System**

1. **Developer Friendly**: Easy testing without affecting live payments
2. **Production Safe**: Clear separation between test and live environments
3. **Seamless Switching**: No downtime when switching modes
4. **Automatic Management**: Handles price recreation and key selection automatically
5. **Visual Clarity**: Clear indicators of current mode and status
6. **Backward Compatible**: Works with existing configurations
7. **Future Proof**: Extensible for additional Stripe features

---

**Note**: This feature significantly improves the development and deployment workflow for Stripe Payment Pro. Always test thoroughly in Test Mode before switching to Live Mode for production use.
