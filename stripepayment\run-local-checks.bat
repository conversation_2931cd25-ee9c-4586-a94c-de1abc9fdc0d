@echo off
setlocal enabledelayedexpansion

REM Local Moodle Plugin CI Checks Script for Windows
REM This script runs the same checks as the GitHub workflow locally

echo Starting local Moodle Plugin CI checks...

REM Set XAMPP paths (modify if your XAMPP is installed elsewhere)
set "XAMPP_PATH=C:\xampp"
set "PHP_PATH=%XAMPP_PATH%\php\php.exe"
set "COMPOSER_PATH=%XAMPP_PATH%\composer\composer.phar"

REM Check if PHP exists in XAMPP
if exist "%PHP_PATH%" (
    echo [INFO] Found PHP at %PHP_PATH%
    set "PHP_CMD=%PHP_PATH%"
) else (
    echo [WARNING] PHP not found in XAMPP, trying system PATH...
    php --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] PHP not found. Please install PHP or check XAMPP installation.
        pause
        exit /b 1
    ) else (
        set "PHP_CMD=php"
        echo [INFO] Using PHP from system PATH
    )
)

REM Check if Composer exists
if exist "%COMPOSER_PATH%" (
    echo [INFO] Found Composer at %COMPOSER_PATH%
    set "COMPOSER_CMD=%PHP_CMD% %COMPOSER_PATH%"
) else (
    echo [WARNING] Composer not found in XAMPP, trying system PATH...
    composer --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Composer not found. Please install Composer.
        pause
        exit /b 1
    ) else (
        set "COMPOSER_CMD=composer"
        echo [INFO] Using Composer from system PATH
    )
)

REM Check if we're in the right directory
if not exist "version.php" (
    echo [ERROR] version.php not found. Please run this script from the plugin root directory.
    exit /b 1
)

REM Install dependencies if needed
echo [INFO] Installing dependencies...
if not exist "vendor" (
    echo [INFO] Installing Composer dependencies...
    %COMPOSER_CMD% install
    if errorlevel 1 (
        echo [ERROR] Failed to install composer dependencies
        pause
        exit /b 1
    )
)

if not exist "node_modules" (
    echo [INFO] Installing NPM dependencies...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install npm dependencies
        pause
        exit /b 1
    )
)

REM Build assets
echo [INFO] Building assets...
npm run build
if errorlevel 1 (
    echo [WARNING] Build failed, continuing...
)

REM Run PHP Lint
echo [INFO] Running PHP Lint...
for /r %%f in (*.php) do (
    echo %%f | findstr /v "vendor node_modules" >nul
    if not errorlevel 1 (
        %PHP_CMD% -l "%%f" >nul
        if errorlevel 1 (
            echo [ERROR] PHP Lint failed for %%f
            pause
            exit /b 1
        )
    )
)
echo [SUCCESS] PHP Lint passed

REM Run PHP CodeSniffer
echo [INFO] Running PHP CodeSniffer...
if exist "vendor\bin\phpcs.bat" (
    %COMPOSER_CMD% run-script phpcs
    if errorlevel 1 (
        echo [WARNING] PHPCS found issues
    ) else (
        echo [SUCCESS] PHPCS check completed
    )
) else (
    echo [WARNING] PHPCS not found, install dev dependencies with: %COMPOSER_CMD% install
)

REM Run PHP Code Beautifier and Fixer
echo [INFO] Running PHP Code Beautifier and Fixer...
if exist "vendor\bin\phpcbf.bat" (
    %COMPOSER_CMD% run-script phpcf
    if errorlevel 1 (
        echo [WARNING] PHPCBF made changes
    ) else (
        echo [SUCCESS] PHPCBF check completed
    )
) else (
    echo [WARNING] PHPCBF not found, install dev dependencies with: %COMPOSER_CMD% install
)

REM Run PHP Mess Detector (if available)
echo [INFO] Running PHP Mess Detector...
if exist "vendor\bin\phpmd.bat" (
    %COMPOSER_CMD% run-script phpmd
    if errorlevel 1 (
        echo [WARNING] PHPMD found issues
    ) else (
        echo [SUCCESS] PHPMD check completed
    )
) else (
    echo [WARNING] PHPMD not found, install dev dependencies with: %COMPOSER_CMD% install
)

REM Validate plugin structure
echo [INFO] Validating plugin structure...
set "required_files=version.php lib.php lang\en\enrol_stripepayment.php"
for %%f in (%required_files%) do (
    if not exist "%%f" (
        echo [ERROR] Required file missing: %%f
        exit /b 1
    )
)
echo [SUCCESS] Plugin structure validation passed

REM Check version.php format
echo [INFO] Checking version.php format...
findstr /c:"plugin->version" version.php >nul && findstr /c:"plugin->component" version.php >nul
if errorlevel 1 (
    echo [ERROR] version.php format is incorrect
    exit /b 1
) else (
    echo [SUCCESS] version.php format is correct
)

REM Build zip package
echo [INFO] Building zip package...
%COMPOSER_CMD% run-script zip
if exist "release\stripepayment.zip" (
    echo [SUCCESS] Zip package created: release\stripepayment.zip
) else (
    echo [ERROR] Failed to create zip package
    pause
    exit /b 1
)

REM Final summary
echo.
echo 🎉 Local checks completed!
echo.
echo 📦 Package: release\stripepayment.zip
echo.
echo To install moodle-plugin-ci for more comprehensive testing:
echo   composer create-project -n --no-dev --prefer-dist moodlehq/moodle-plugin-ci ci ^4
echo   set PATH=%%PATH%%;%%CD%%\ci\bin;%%CD%%\ci\vendor\bin
echo   moodle-plugin-ci install --plugin . --db-host=127.0.0.1
echo.
echo Then run additional checks:
echo   moodle-plugin-ci phplint .
echo   moodle-plugin-ci phpcpd .
echo   moodle-plugin-ci phpmd .
echo   moodle-plugin-ci phpcs .
echo   moodle-plugin-ci phpcbf .
echo   moodle-plugin-ci validate .
echo   moodle-plugin-ci mustache .
echo   moodle-plugin-ci grunt .
echo   moodle-plugin-ci savepoints .
echo   moodle-plugin-ci phpunit .
echo   moodle-plugin-ci behat .

pause
