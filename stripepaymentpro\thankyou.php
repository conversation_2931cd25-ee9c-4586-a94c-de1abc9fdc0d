<?php

// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin - Simple thank you page (Old Plugin Style)
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->libdir . '/enrollib.php');
require_once($CFG->libdir . '/moodlelib.php');
require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');
use \Stripe\Stripe;
use \Stripe\Checkout\Session;
use \Stripe\PaymentIntent;

// Set up Stripe API
$plugin = enrol_get_plugin('stripepaymentpro');
Stripe::setApiKey($plugin->get_current_secret_key());

// Get parameters - support both session_id and payment_intent
$session_id = optional_param('session_id', '', PARAM_RAW);
$payment_intent_id = optional_param('payment_intent', '', PARAM_RAW);

try {
    if (!empty($session_id)) {
        // Handle Checkout Session
        $session = Session::retrieve($session_id);

        // Check if the payment status is 'paid'
        if ($session->payment_status === 'paid') {
            // Retrieve metadata
            $instance_id = $session->metadata->instanceid;
            $user_id = $session->metadata->userid;

            // Get the course ID from the enrolment instance
            $enrol_instance = $DB->get_record('enrol', ['id' => $instance_id], '*', MUST_EXIST);
            $course = $DB->get_record('course', ['id' => $enrol_instance->courseid], '*', MUST_EXIST);
            // Redirect to the course view page - NO ENROLLMENT PROCESSING HERE
            redirect(new moodle_url('/course/view.php', ['id' => $course->id]));
        } else {
            // If payment status is not 'paid', display an error message
            echo 'Payment was not successful. Please try again.';
            die();
        }

    } elseif (!empty($payment_intent_id)) {
        // Handle PaymentIntent (Elements mode)
        $payment_intent = PaymentIntent::retrieve($payment_intent_id);

        // Check if the payment status is 'succeeded'
        if ($payment_intent->status === 'succeeded') {
            // Retrieve metadata
            $instance_id = $payment_intent->metadata->instanceid;
            $user_id = $payment_intent->metadata->userid;

            // Get the course ID from the enrolment instance
            $enrol_instance = $DB->get_record('enrol', ['id' => $instance_id], '*', MUST_EXIST);
            $course = $DB->get_record('course', ['id' => $enrol_instance->courseid], '*', MUST_EXIST);

            // Redirect to the course view page - NO ENROLLMENT PROCESSING HERE
            redirect(new moodle_url('/course/view.php', ['id' => $course->id]));
        } else {
            // If payment status is not 'succeeded', display an error message
            echo 'Payment was not successful. Please try again.';
            die();
        }

    } else {
        // No valid payment session found
        echo 'Invalid payment session. Please try again.';
        die();
    }

} catch (Exception $e) {
    // Handle error
    \core\notification::error($e->getMessage());
    echo 'Error: ' . $e->getMessage();
    die();
}
