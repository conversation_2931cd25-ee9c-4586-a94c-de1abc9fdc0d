# Local Moodle Plugin CI Checks Script for PowerShell
# This script runs the same checks as the GitHub workflow locally

param(
    [string]$XamppPath = "C:\xampp"
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

Write-Host "🚀 Starting local Moodle Plugin CI checks..." -ForegroundColor $Blue

# Check if we're in the right directory
if (-not (Test-Path "version.php")) {
    Write-Error "version.php not found. Please run this script from the plugin root directory."
    exit 1
}

# Setup paths for XAMPP
$phpPath = Join-Path $XamppPath "php\php.exe"
$composerPath = Join-Path $XamppPath "composer\composer.phar"

# Check if PHP exists
if (-not (Test-Path $phpPath)) {
    Write-Warning "PHP not found at $phpPath. Trying system PATH..."
    try {
        $phpPath = (Get-Command php).Source
        Write-Success "Found PHP at $phpPath"
    } catch {
        Write-Error "PHP not found. Please install PHP or update the XamppPath parameter."
        exit 1
    }
} else {
    Write-Success "Found PHP at $phpPath"
}

# Check if Composer exists
if (-not (Test-Path $composerPath)) {
    Write-Warning "Composer not found at $composerPath. Trying system PATH..."
    try {
        $composerCmd = (Get-Command composer).Source
        $composerPath = "composer"
        Write-Success "Found Composer in system PATH"
    } catch {
        Write-Error "Composer not found. Please install Composer."
        exit 1
    }
} else {
    $composerPath = "$phpPath $composerPath"
    Write-Success "Found Composer at $composerPath"
}

# Check for Node.js
try {
    $nodeVersion = node --version
    Write-Success "Found Node.js version: $nodeVersion"
} catch {
    Write-Error "Node.js not found. Please install Node.js."
    exit 1
}

# Install dependencies if needed
Write-Status "Installing dependencies..."
if (-not (Test-Path "vendor")) {
    Write-Status "Installing Composer dependencies..."
    Invoke-Expression "$composerPath install"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install Composer dependencies"
        exit 1
    }
}

if (-not (Test-Path "node_modules")) {
    Write-Status "Installing NPM dependencies..."
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install NPM dependencies"
        exit 1
    }
}

# Build assets
Write-Status "Building assets..."
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Warning "Build failed, continuing..."
}

# Run PHP Lint
Write-Status "Running PHP Lint..."
$phpFiles = Get-ChildItem -Recurse -Filter "*.php" | Where-Object { 
    $_.FullName -notmatch "vendor" -and $_.FullName -notmatch "node_modules" 
}

foreach ($file in $phpFiles) {
    & $phpPath -l $file.FullName | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "PHP Lint failed for $($file.FullName)"
        exit 1
    }
}
Write-Success "PHP Lint passed"

# Run PHP CodeSniffer
Write-Status "Running PHP CodeSniffer..."
if (Test-Path "vendor\bin\phpcs.bat") {
    Invoke-Expression "$composerPath run-script phpcs"
    if ($LASTEXITCODE -ne 0) {
        Write-Warning "PHPCS found issues"
    } else {
        Write-Success "PHPCS check completed"
    }
} else {
    Write-Warning "PHPCS not found, install dev dependencies with: composer install"
}

# Run PHP Code Beautifier and Fixer
Write-Status "Running PHP Code Beautifier and Fixer..."
if (Test-Path "vendor\bin\phpcbf.bat") {
    Invoke-Expression "$composerPath run-script phpcf"
    if ($LASTEXITCODE -ne 0) {
        Write-Warning "PHPCBF made changes"
    } else {
        Write-Success "PHPCBF check completed"
    }
} else {
    Write-Warning "PHPCBF not found, install dev dependencies with: composer install"
}

# Validate plugin structure
Write-Status "Validating plugin structure..."
$requiredFiles = @("version.php", "lib.php", "lang\en\enrol_stripepayment.php")
foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Error "Required file missing: $file"
        exit 1
    }
}
Write-Success "Plugin structure validation passed"

# Check version.php format
Write-Status "Checking version.php format..."
$versionContent = Get-Content "version.php" -Raw
if ($versionContent -match "plugin->version" -and $versionContent -match "plugin->component") {
    Write-Success "version.php format is correct"
} else {
    Write-Error "version.php format is incorrect"
    exit 1
}

# Create release directory
if (-not (Test-Path "release")) {
    New-Item -ItemType Directory -Path "release" | Out-Null
}

# Build zip package
Write-Status "Building zip package..."
Invoke-Expression "$composerPath run-script zip"
if (Test-Path "release\stripepayment.zip") {
    Write-Success "Zip package created: release\stripepayment.zip"
} else {
    Write-Error "Failed to create zip package"
    exit 1
}

# Final summary
Write-Host ""
Write-Host "🎉 Local checks completed!" -ForegroundColor $Green
Write-Host ""
Write-Host "📦 Package: release\stripepayment.zip" -ForegroundColor $Blue
Write-Host ""
Write-Host "To install moodle-plugin-ci for more comprehensive testing:" -ForegroundColor $Yellow
Write-Host "  composer create-project -n --no-dev --prefer-dist moodlehq/moodle-plugin-ci ci ^4"
Write-Host "  `$env:PATH += `";`$(Get-Location)\ci\bin;`$(Get-Location)\ci\vendor\bin`""
Write-Host "  moodle-plugin-ci install --plugin . --db-host=127.0.0.1"
Write-Host ""
Write-Host "Then run additional checks:" -ForegroundColor $Yellow
Write-Host "  moodle-plugin-ci phplint ."
Write-Host "  moodle-plugin-ci phpcpd ."
Write-Host "  moodle-plugin-ci phpmd ."
Write-Host "  moodle-plugin-ci phpcs ."
Write-Host "  moodle-plugin-ci phpcbf ."
Write-Host "  moodle-plugin-ci validate ."
Write-Host "  moodle-plugin-ci mustache ."
Write-Host "  moodle-plugin-ci grunt ."
Write-Host "  moodle-plugin-ci savepoints ."
Write-Host "  moodle-plugin-ci phpunit ."
Write-Host "  moodle-plugin-ci behat ."
