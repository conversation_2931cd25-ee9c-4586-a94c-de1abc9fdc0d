<?php
/**
 * PHPUnit tests for webhook functionality in stripepaymentpro plugin
 * 
 * Run with: php admin/tool/phpunit/cli/util.php --install
 * Then: vendor/bin/phpunit enrol/stripepaymentpro/tests/webhook_phpunit_test.php
 * 
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once($CFG->dirroot . '/enrol/stripepaymentpro/classes/helper/webhook_helper.php');

/**
 * PHPUnit test class for webhook functionality
 */
class enrol_stripepaymentpro_webhook_testcase extends advanced_testcase {

    /**
     * Test webhook configuration
     */
    public function test_webhook_configuration() {
        $this->resetAfterTest();

        // Test webhook ID configuration
        set_config('stripe_webhook_id', 'we_test123', 'enrol_stripepaymentpro');
        $webhook_id = get_config('enrol_stripepaymentpro', 'stripe_webhook_id');
        $this->assertEquals('we_test123', $webhook_id);

        // Test webhook secret configuration
        set_config('stripe_webhook_secret', 'whsec_test123', 'enrol_stripepaymentpro');
        $webhook_secret = get_config('enrol_stripepaymentpro', 'stripe_webhook_secret');
        $this->assertEquals('whsec_test123', $webhook_secret);
    }

    /**
     * Test database table structure
     */
    public function test_database_table_structure() {
        global $DB;
        $this->resetAfterTest();

        // Check if the enrol_stripepaymentpro table exists
        $table_exists = $DB->get_manager()->table_exists('enrol_stripepaymentpro');
        $this->assertTrue($table_exists, 'enrol_stripepaymentpro table should exist');

        // Check table structure
        $columns = $DB->get_columns('enrol_stripepaymentpro');
        $expected_columns = [
            'id', 'courseid', 'instanceid', 'userid', 'coupon_id',
            'stripeEmail', 'currency', 'receiver_id', 'renewal_interval',
            'renewal_intervalperiod', 'trialperiodend', 'subscription_id',
            'pending_reason', 'product_id', 'product_name', 'product_type',
            'receiver_email', 'txn_id', 'tax', 'memo', 'payment_status',
            'item_name', 'timeupdated'
        ];

        foreach ($expected_columns as $column) {
            $this->assertArrayHasKey($column, $columns, "Column '$column' should exist in enrol_stripepaymentpro table");
        }
    }

    /**
     * Test webservice function registration
     */
    public function test_webservice_function_registration() {
        global $DB;
        $this->resetAfterTest();

        // Check if webhook handler function is registered
        $function = $DB->get_record('external_functions', ['name' => 'moodle_stripepaymentpro_webhook_handler']);
        $this->assertNotFalse($function, 'Webhook handler webservice function should be registered');

        // Check function properties
        $this->assertEquals('moodle_enrol_stripepaymentpro_external', $function->classname);
        $this->assertEquals('webhook_handler', $function->methodname);
        $this->assertEquals('enrol/stripepaymentpro/externallib.php', $function->classpath);
    }

    /**
     * Test enrolment instance creation
     */
    public function test_enrolment_instance_creation() {
        global $DB;
        $this->resetAfterTest();

        // Create a test course
        $course = $this->getDataGenerator()->create_course();

        // Create a test user
        $user = $this->getDataGenerator()->create_user();

        // Get the stripepaymentpro plugin
        $plugin = enrol_get_plugin('stripepaymentpro');

        // Add an instance
        $instance_id = $plugin->add_instance($course, [
            'cost' => 100,
            'currency' => 'USD',
            'roleid' => 5, // Student role
        ]);

        $this->assertNotNull($instance_id);

        // Check the instance was created
        $instance = $DB->get_record('enrol', ['id' => $instance_id]);
        $this->assertNotFalse($instance);
        $this->assertEquals('stripepaymentpro', $instance->enrol);
        $this->assertEquals($course->id, $instance->courseid);
    }

    /**
     * Test user enrolment
     */
    public function test_user_enrolment() {
        global $DB;
        $this->resetAfterTest();

        // Create a test course
        $course = $this->getDataGenerator()->create_course();

        // Create a test user
        $user = $this->getDataGenerator()->create_user();

        // Get the stripepaymentpro plugin
        $plugin = enrol_get_plugin('stripepaymentpro');

        // Add an instance
        $instance_id = $plugin->add_instance($course, [
            'cost' => 100,
            'currency' => 'USD',
            'roleid' => 5, // Student role
        ]);

        $instance = $DB->get_record('enrol', ['id' => $instance_id]);

        // Enrol the user
        $plugin->enrol_user($instance, $user->id, $instance->roleid);

        // Check the user is enrolled
        $context = context_course::instance($course->id);
        $this->assertTrue(is_enrolled($context, $user->id));

        // Check the enrolment record
        $enrolment = $DB->get_record('user_enrolments', [
            'enrolid' => $instance_id,
            'userid' => $user->id
        ]);
        $this->assertNotFalse($enrolment);
    }

    /**
     * Test payment record creation
     */
    public function test_payment_record_creation() {
        global $DB;
        $this->resetAfterTest();

        // Create test data
        $course = $this->getDataGenerator()->create_course();
        $user = $this->getDataGenerator()->create_user();

        $plugin = enrol_get_plugin('stripepaymentpro');
        $instance_id = $plugin->add_instance($course, [
            'cost' => 100,
            'currency' => 'USD',
            'roleid' => 5,
        ]);

        // Create a payment record
        $payment_data = new stdClass();
        $payment_data->courseid = $course->id;
        $payment_data->instanceid = $instance_id;
        $payment_data->userid = $user->id;
        $payment_data->stripeEmail = $user->email;
        $payment_data->currency = 'USD';
        $payment_data->product_id = 'prod_test123';
        $payment_data->product_name = $course->fullname;
        $payment_data->product_type = 'one_time';
        $payment_data->receiver_email = $user->email;
        $payment_data->txn_id = 'pm_test123';
        $payment_data->tax = 100;
        $payment_data->payment_status = 'paid';
        $payment_data->item_name = $course->fullname;
        $payment_data->timeupdated = time();

        $record_id = $DB->insert_record('enrol_stripepaymentpro', $payment_data);
        $this->assertNotFalse($record_id);

        // Verify the record was created
        $record = $DB->get_record('enrol_stripepaymentpro', ['id' => $record_id]);
        $this->assertNotFalse($record);
        $this->assertEquals($course->id, $record->courseid);
        $this->assertEquals($user->id, $record->userid);
        $this->assertEquals('paid', $record->payment_status);
    }
}
