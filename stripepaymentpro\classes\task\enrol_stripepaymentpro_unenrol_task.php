<?php

// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\task;

defined('MOODLE_INTERNAL') || die;

require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');

use Stripe\Stripe as Stripe;
use Stripe\Subscription as Subscription;

/**
 * Stripe enrolment plugin.
 *
 * schedule task for unenrol if the  user's subscription is de activated
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class enrol_stripepaymentpro_unenrol_task extends \core\task\scheduled_task {
    /**
     * Get a descriptive name for this task (shown to admins).
     *
     * @return string
     */
    public function get_name() {
        return get_string('pluginname', 'enrol_stripepaymentpro');
    }

    /**
     * Execute.
     */
    public function execute() {
        global $DB, $CFG;

        mtrace("\n" . get_string('taskstart', 'enrol_stripepaymentpro'));

        // Log task start using Moodle's event system
        // \core\event\scheduled_task_started::create(['task' => get_class($this)])->trigger();
        try {
            $plugin = enrol_get_plugin('stripepaymentpro');
            $secretkey = $plugin->get_current_secret_key();

            if (empty($secretkey)) {
                mtrace("Error: Secret key is not set.");
                return;
            }

            Stripe::setApiKey($secretkey);

            $stripepaymentproenroldb = $DB->get_records('enrol_stripepaymentpro');

            mtrace("Processing " . count($stripepaymentproenroldb) . " enrollments.");

            foreach ($stripepaymentproenroldb as $enrolment) {
                mtrace($enrolment->receiver_email . ' => ' . $enrolment->subscription_id . ' ' . get_string('checking', 'enrol_stripepaymentpro'));

                try {
                    $subscription = Subscription::retrieve($enrolment->subscription_id);

                    // Check if the subscription is canceled and if the current period end is past compared to today
                    $currentperiodend = $subscription->current_period_end;
                    $currentDate = time();

                    if ($subscription->status == 'canceled' && $currentperiodend < $currentDate) {
                        $instance = $DB->get_record('enrol', array('id'=>$enrolment->instanceid, "enrol" => "stripepaymentpro"), '*', MUST_EXIST);
                        $plugin->unenrol_user($instance, $enrolment->userid);
                        $DB->delete_records('enrol_stripepaymentpro', array('subscription_id' => $enrolment->subscription_id));
                        mtrace($enrolment->receiver_email . ' ' . get_string('unenrol', 'enrol_stripepaymentpro') . ' ' . $enrolment->item_name);
                    }
                } catch (\Exception $e) {
                    mtrace("Error processing subscription {$enrolment->subscription_id}: " . $e->getMessage());
                }
            }
        } catch (\Exception $e) {
            mtrace("An error occurred during task execution: " . $e->getMessage());
        }
        mtrace(get_string('taskend', 'enrol_stripepaymentpro'));
    }
}
