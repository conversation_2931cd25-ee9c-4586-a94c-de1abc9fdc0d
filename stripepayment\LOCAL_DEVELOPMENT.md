# Local Development Guide

This guide explains how to run the same quality checks locally that are performed in the GitHub Actions workflow.

## Quick Start

### Windows
```bash
run-local-checks.bat
```

### Linux/macOS
```bash
chmod +x run-local-checks.sh
./run-local-checks.sh
```

## Prerequisites

1. **PHP 8.0+** with extensions: mbstring, intl, soap, curl, gd, xml, json, zip, pdo, pdo_mysql
2. **Node.js 18+** and npm
3. **Composer**
4. **Git**

## Manual Setup

### 1. Install Dependencies

```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 2. Build Assets

```bash
# Build JavaScript/CSS assets
npm run build
```

## Available Composer Scripts

```bash
# Build assets
composer run-script build

# Create zip package (saves to current directory and release/)
composer run-script zip

# Create zip package directly in release/
composer run-script zip-release

# Run PHP CodeSniffer
composer run-script phpcs

# Run PHP Code Beautifier and Fixer
composer run-script phpcf

# Run PHP Mess Detector
composer run-script phpmd
```

## Individual Quality Checks

### PHP Lint
```bash
# Check PHP syntax
find . -name "*.php" -not -path "./vendor/*" -not -path "./node_modules/*" -exec php -l {} \;
```

### PHP CodeSniffer (PHPCS)
```bash
# Check coding standards
composer run-script phpcs

# Fix coding standards automatically
composer run-script phpcf
```

### PHP Mess Detector
```bash
# Detect potential issues
composer run-script phpmd
```

## Full Moodle Plugin CI Setup

For comprehensive testing that matches the GitHub workflow exactly:

### 1. Install Moodle Plugin CI

```bash
composer create-project -n --no-dev --prefer-dist moodlehq/moodle-plugin-ci ci ^4
export PATH=$PATH:$(pwd)/ci/bin:$(pwd)/ci/vendor/bin
```

### 2. Setup Database (MariaDB/MySQL)

```bash
# Start MariaDB/MySQL service
# Create empty database for testing
mysql -u root -e "CREATE DATABASE moodle_test;"
```

### 3. Install Moodle Plugin CI

```bash
moodle-plugin-ci install --plugin . --db-host=127.0.0.1
```

### 4. Run All Checks

```bash
# PHP Lint
moodle-plugin-ci phplint .

# PHP Copy/Paste Detector
moodle-plugin-ci phpcpd .

# PHP Mess Detector
moodle-plugin-ci phpmd .

# Moodle Code Checker (PHPCS)
moodle-plugin-ci phpcs .

# PHP Code Beautifier and Fixer
moodle-plugin-ci phpcbf .

# Validate Plugin
moodle-plugin-ci validate .

# Check Mustache Templates
moodle-plugin-ci mustache .

# Check Grunt Build
moodle-plugin-ci grunt .

# Check Upgrade Savepoints
moodle-plugin-ci savepoints .

# PHPUnit Tests
moodle-plugin-ci phpunit --fail-on-warning .

# Behat Tests (requires browser setup)
moodle-plugin-ci behat --profile chrome .
```

## Environment Variables

For full CI testing, you may need to set:

```bash
export DB=mariadb
export MOODLE_BRANCH=MOODLE_500_STABLE
```

## Troubleshooting

### Common Issues

1. **Permission denied on scripts**
   ```bash
   chmod +x run-local-checks.sh
   ```

2. **PHP extensions missing**
   - Install required PHP extensions listed in prerequisites

3. **Node.js version issues**
   - Use Node.js 18+ (check with `node --version`)

4. **Composer memory issues**
   ```bash
   php -d memory_limit=-1 $(which composer) install
   ```

5. **Database connection issues**
   - Ensure MariaDB/MySQL is running
   - Check connection credentials
   - Verify database exists

### Debugging

Enable verbose output:
```bash
# For composer scripts
composer run-script phpcs -vvv

# For moodle-plugin-ci
moodle-plugin-ci phplint . --verbose
```

## Release Process

1. Update version in `version.php`
2. Update `CHANGELOG.md`
3. Run local checks: `./run-local-checks.sh`
4. Commit changes
5. Create and push tag: `git tag v1.0.0 && git push origin v1.0.0`
6. GitHub Actions will automatically create release with zip file

## File Structure

```
stripepayment/
├── .github/workflows/moodle-ci.yml  # CI workflow
├── run-local-checks.sh              # Linux/macOS local checks
├── run-local-checks.bat             # Windows local checks
├── run-local-checks.ps1             # Windows PowerShell local checks
├── composer.json                    # PHP dependencies & scripts
├── package.json                     # Node.js dependencies
├── phpcs.xml                        # PHP CodeSniffer config
├── .zipignore                       # Files to exclude from zip
└── release/                         # Generated zip files
```
