<?php
/**
 * Test webhook enrolment functionality for stripepaymentpro plugin
 * 
 * This script helps test and verify that webhook enrolment is working correctly.
 * 
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->dirroot . '/enrol/stripepaymentpro/classes/helper/webhook_helper.php');

// Require admin login
require_login();
if (!is_siteadmin()) {
    throw new moodle_exception('nopermissions', 'error', '', 'Access denied');
}

$PAGE->set_url('/enrol/stripepaymentpro/test_webhook.php');
$PAGE->set_context(context_system::instance());
$PAGE->set_title('Webhook Test - Stripe Payment Pro');
$PAGE->set_heading('Webhook Enrolment Test');

echo $OUTPUT->header();

echo '<div class="container-fluid">';
echo '<h2>Webhook Enrolment Test for Stripe Payment Pro</h2>';

// Test 1: Check webhook configuration
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Test 1: Webhook Configuration</h4></div>';
echo '<div class="card-body">';

$webhook_id = get_config('enrol_stripepaymentpro', 'stripe_webhook_id');
$webhook_secret = get_config('enrol_stripepaymentpro', 'stripe_webhook_secret');
$webservice_token = get_config('enrol_stripepaymentpro', 'webservice_token');

if ($webhook_id) {
    echo '<p class="text-success">✅ Webhook ID configured: ' . $webhook_id . '</p>';
} else {
    echo '<p class="text-danger">❌ Webhook ID not configured</p>';
}

if ($webhook_secret) {
    echo '<p class="text-success">✅ Webhook secret configured</p>';
} else {
    echo '<p class="text-danger">❌ Webhook secret not configured</p>';
}

if ($webservice_token) {
    echo '<p class="text-success">✅ Webservice token configured</p>';
} else {
    echo '<p class="text-danger">❌ Webservice token not configured</p>';
}

echo '</div></div>';

// Test 2: Check webhook URL accessibility
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Test 2: Webhook URL</h4></div>';
echo '<div class="card-body">';

$webhook_url = $CFG->wwwroot . '/webservice/rest/server.php?wstoken=' . $webservice_token . '&wsfunction=moodle_stripepaymentpro_webhook_handler';
echo '<p><strong>Webhook URL:</strong></p>';
echo '<code>' . $webhook_url . '</code>';

echo '</div></div>';

// Test 3: Check Stripe API connectivity
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Test 3: Stripe API Connectivity</h4></div>';
echo '<div class="card-body">';

try {
    $plugin = enrol_get_plugin('stripepaymentpro');
    $secret_key = $plugin->get_current_secret_key();
    
    if ($secret_key) {
        echo '<p class="text-success">✅ Stripe secret key configured</p>';
        
        // Test Stripe connection
        require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');
        \Stripe\Stripe::setApiKey($secret_key);
        
        try {
            $account = \Stripe\Account::retrieve();
            echo '<p class="text-success">✅ Stripe API connection successful</p>';
            echo '<p>Account ID: ' . $account->id . '</p>';
        } catch (Exception $e) {
            echo '<p class="text-danger">❌ Stripe API connection failed: ' . $e->getMessage() . '</p>';
        }
    } else {
        echo '<p class="text-danger">❌ Stripe secret key not configured</p>';
    }
} catch (Exception $e) {
    echo '<p class="text-danger">❌ Error checking Stripe configuration: ' . $e->getMessage() . '</p>';
}

echo '</div></div>';

// Test 4: Check database tables
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Test 4: Database Tables</h4></div>';
echo '<div class="card-body">';

try {
    $table_exists = $DB->get_manager()->table_exists('enrol_stripepaymentpro');
    if ($table_exists) {
        echo '<p class="text-success">✅ enrol_stripepaymentpro table exists</p>';
        
        $record_count = $DB->count_records('enrol_stripepaymentpro');
        echo '<p>Total payment records: ' . $record_count . '</p>';
    } else {
        echo '<p class="text-danger">❌ enrol_stripepaymentpro table does not exist</p>';
    }
} catch (Exception $e) {
    echo '<p class="text-danger">❌ Error checking database: ' . $e->getMessage() . '</p>';
}

echo '</div></div>';

// Test 5: Check webservice function
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Test 5: Webservice Function</h4></div>';
echo '<div class="card-body">';

try {
    $functions = $DB->get_records('external_functions', ['name' => 'moodle_stripepaymentpro_webhook_handler']);
    if (!empty($functions)) {
        echo '<p class="text-success">✅ Webhook handler webservice function is registered</p>';
    } else {
        echo '<p class="text-danger">❌ Webhook handler webservice function is not registered</p>';
    }
} catch (Exception $e) {
    echo '<p class="text-danger">❌ Error checking webservice function: ' . $e->getMessage() . '</p>';
}

echo '</div></div>';

// Instructions
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Testing Instructions</h4></div>';
echo '<div class="card-body">';
echo '<ol>';
echo '<li>Ensure all tests above pass (show green checkmarks)</li>';
echo '<li>Create a test course with Stripe Payment Pro enrolment</li>';
echo '<li>Make a test payment using Stripe test cards</li>';
echo '<li>Check if the user gets enrolled automatically via webhook</li>';
echo '<li>Monitor the enrol_stripepaymentpro table for new payment records</li>';
echo '</ol>';

echo '<h5>Stripe Test Cards:</h5>';
echo '<ul>';
echo '<li><strong>****************</strong> - Visa (succeeds)</li>';
echo '<li><strong>****************</strong> - Visa (card declined)</li>';
echo '<li><strong>****************</strong> - Visa (insufficient funds)</li>';
echo '</ul>';

echo '</div></div>';

echo '</div>';

echo $OUTPUT->footer();
