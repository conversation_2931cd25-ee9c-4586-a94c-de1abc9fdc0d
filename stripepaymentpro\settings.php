<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe Pro enrolment plugin settings.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use enrol_stripepaymentpro\controller\enrol_stripepaymentpro_license_controller;
use enrol_stripepaymentpro\controller\enrol_stripepaymentpro_webhook_controller;

defined('MOODLE_INTERNAL') || die();
global $CFG;
require_once($CFG->dirroot . '/enrol/stripepaymentpro/lib.php');

// Handle license activation/deactivation.
if (optional_param('stripepaymentpro_license_set_status', '', PARAM_RAW)) {
    $license_controller = new enrol_stripepaymentpro_license_controller();
    $licensedata = $license_controller->add_data();
    if (isset($licensedata) && $licensedata != null) {
        if (isset($licensedata->error)) {
            \core\notification::error($licensedata->error);
        } else {
            $activations_remaining = $licensedata->data->activations_remaining;
            $total_activations_purchased = $licensedata->data->total_activations_purchased;

            if (isset($licensedata->deactivated)) {
                $message = "License deactivated successfully. {$activations_remaining} out of {$total_activations_purchased} activations remaining. Your webhook endpoint is deleted";
            } else if (isset($licensedata->activated)) {
                $message = "License activated successfully. {$activations_remaining} out of {$total_activations_purchased} activations remaining. Your webhook endpoint is ready";
            }
            \core\notification::success($message);
            redirect($CFG->wwwroot . '/admin/settings.php?section=enrolsettingsstripepaymentpro');
        }
    }
}

// Set default license status.
if (strpos(get_config('enrol_stripepaymentpro', 'license_status'), 'active') === false) {
    set_config("license_status", 'inactive', 'enrol_stripepaymentpro');
}

if ($hassiteconfig) {
    $ADMIN->add(
        'enrolments',
        new admin_externalpage(
            'enrol_stripepaymentpro/couponmanagement',
            get_string('stripepaymentpro_couponmanagement_menu_name', 'enrol_stripepaymentpro'),
            new moodle_url('/enrol/stripepaymentpro/coupon_management.php'),
            'moodle/site:config'
        )
    );
}

if ($ADMIN->fulltree) {
    // General settings.
    $settings->add(new admin_setting_heading(
        'enrol_stripepaymentpro_settings',
        '',
        get_string('pluginname_desc', 'enrol_stripepaymentpro')
    ));

    // Webservice token.
    $webservicesoverview = $CFG->wwwroot . '/admin/search.php?query=enablewebservices';
    $restweblink = $CFG->wwwroot . '/admin/settings.php?section=webserviceprotocols';
    $createtoken = $CFG->wwwroot . '/admin/webservice/tokens.php';
    $settings->add(new admin_setting_configtext(
        'enrol_stripepaymentpro/webservice_token',
        get_string('webservice_token_string', 'enrol_stripepaymentpro'),
        get_string('enablewebservicesfirst', 'enrol_stripepayment') . '<a href="' . $webservicesoverview . '" target="_blank"> '
        . get_string('from_here', 'enrol_stripepaymentpro') . '</a> . '
        . get_string('create_user_token', 'enrol_stripepaymentpro') . '<a href="' . $restweblink . '" target="_blank"> '
        . get_string('from_here', 'enrol_stripepaymentpro') . '</a> . '
        . get_string('enabled_rest_protocol', 'enrol_stripepaymentpro') . '<a href="' . $createtoken . '" target="_blank"> '
        . get_string('from_here', 'enrol_stripepaymentpro') . '</a>',
        ''
    ));

    // Payment gateway type.
    $payment_gateway_options = [
        'checkout' => get_string('stripe_checkout', 'enrol_stripepaymentpro'),
        'elements' => get_string('stripe_elements', 'enrol_stripepaymentpro'),
    ];
    $settings->add(new admin_setting_configselect(
        'enrol_stripepaymentpro/payment_gateway_type',
        get_string('payment_gateway_type', 'enrol_stripepaymentpro'),
        get_string('payment_gateway_type_desc', 'enrol_stripepaymentpro'),
        'checkout',
        $payment_gateway_options
    ));

    // Automatic tax calculation.
    $settings->add(new admin_setting_configcheckbox(
        'enrol_stripepaymentpro/enable_automatic_tax',
        get_string('enable_automatic_tax', 'enrol_stripepaymentpro'),
        '',
        0
    ));

    // Coupon settings.
    $coupon_setting_url = $CFG->wwwroot . '/enrol/stripepaymentpro/coupon_management.php';
    $coupon_setting_link_text = get_string('coupon_setting_link_text', 'enrol_stripepaymentpro');
    $settings->add(new admin_setting_heading(
        'enrol_stripepaymentpro_coupon_settings',
        get_string('coupon_settings', 'enrol_stripepaymentpro'),
        get_string('coupon_settings_desc', 'enrol_stripepaymentpro') . ' <a href="' . $coupon_setting_url . '">' . $coupon_setting_link_text . '</a>'
    ));

    // License settings.
    $settings->add(new admin_setting_heading(
        'enrol_stripepaymentpro_license_settings',
        get_string('licensesetting', 'enrol_stripepaymentpro'),
        ''
    ));

    $settings->add(new admin_setting_configtext(
        'enrol_stripepaymentpro/apikey',
        get_string('apikey', 'enrol_stripepaymentpro'),
        '<a href="' . get_string('subscription_url', 'enrol_stripepaymentpro') . '" target="_blank">' . get_string('apikey', 'enrol_stripepaymentpro') . '</a>' . get_string('apikey_desc', 'enrol_stripepaymentpro'),
        '',
        PARAM_TEXT
    ));

    $settings->add(new admin_setting_configtext(
        'enrol_stripepaymentpro/productid',
        get_string('productid', 'enrol_stripepaymentpro'),
        get_string('productid_desc', 'enrol_stripepaymentpro') . ' <a href="' . get_string('subscription_url', 'enrol_stripepaymentpro') . '" target="_blank">' . get_string('productid', 'enrol_stripepaymentpro') . '</a>',
        '',
        PARAM_TEXT
    ));

    // License status display.
    $settings->add(new admin_setting_description(
        'enrol_stripepaymentpro/license_status',
        get_string('licensestatus', 'enrol_stripepaymentpro'),
        get_config('enrol_stripepaymentpro', 'license_status')
    ));

    // License expiry display.
    $orderid = get_config('enrol_stripepaymentpro', 'subscriptionid');
    if (get_config('enrol_stripepaymentpro', 'expirey') === 'When Cancelled') {
        $expirydisplay = get_config('enrol_stripepaymentpro', 'expirey_day');
    } else {
        $expirydisplay = get_string('expired', 'enrol_stripepaymentpro') . ' <a href="' .
            get_string('subscriptionrenew_url', 'enrol_stripepaymentpro') . $orderid . '" target="_blank">' .
            get_string('renewnow', 'enrol_stripepaymentpro') . '</a>';
    }
    $settings->add(new admin_setting_description(
        'enrol_stripepaymentpro/expiry',
        get_string('licenseexdate', 'enrol_stripepaymentpro'),
        $expirydisplay
    ));

    // License activation/deactivation button.
    if (get_config('enrol_stripepaymentpro', 'license_status') === 'active') {
        $buttontext = get_string('deactivelicense', 'enrol_stripepaymentpro');
    } else {
        $buttontext = get_string('activelicense', 'enrol_stripepaymentpro');
    }
    $settings->add(new admin_setting_description(
        'enrol_stripepaymentpro/activate_license',
        '',
        '<button type="submit" class="btn btn-primary text-white" name="stripepaymentpro_license_set_status" value="' . $buttontext . '">' . $buttontext . '</button>'
    ));

    // Manage subscription menu items.
    if (optional_param('section', '', PARAM_TEXT) == 'enrolsettingsstripepaymentpro' ||
        stripos($_SERVER['REQUEST_URI'], 'upgradesettings.php') !== false) {

        // Custom menu items.
        $currentcustommenuitems = str_replace(["\r\n", "\r"], "\n", $CFG->custommenuitems);
        $lines = explode("\n", $currentcustommenuitems);
        $lines = array_map('trim', $lines);
        $subscriptionmenu = 'My Subscription|/enrol/stripepaymentpro/subscription.php';

        if (get_config('enrol_stripepaymentpro', 'license_status') === 'active') {
            if (!in_array($subscriptionmenu, $lines)) {
                array_splice($lines, 1, 0, [$subscriptionmenu]);
                set_config('custommenuitems', implode("\n", $lines));
            }
        } else {
            if (in_array($subscriptionmenu, $lines)) {
                $lines = array_diff($lines, [$subscriptionmenu]);
                set_config('custommenuitems', implode("\n", $lines));
            }
        }

        // Custom user menu items.
        $currentcustomusermenuitems = str_replace(["\r\n", "\r"], "\n", $CFG->customusermenuitems);
        $userlines = explode("\n", $currentcustomusermenuitems);
        $userlines = array_map('trim', $userlines);
        $usersubscriptionmenu = 'My Subscription|/enrol/stripepaymentpro/subscription.php';

        if (get_config('enrol_stripepaymentpro', 'license_status') === 'active') {
            if (!in_array($usersubscriptionmenu, $userlines)) {
                array_splice($userlines, 1, 0, [$usersubscriptionmenu]);
                set_config('customusermenuitems', implode("\n", $userlines));
            }
            // Create webhook endpoint if not exists.
            if (!get_config('enrol_stripepaymentpro', 'stripe_webhook_id')) {
                $webhookcontroller = new enrol_stripepaymentpro_webhook_controller();
                $webhookcontroller->create_webhook();
            }
        } else {
            if (in_array($usersubscriptionmenu, $userlines)) {
                $userlines = array_diff($userlines, [$usersubscriptionmenu]);
                set_config('customusermenuitems', implode("\n", $userlines));
            }
        }
    }
}
