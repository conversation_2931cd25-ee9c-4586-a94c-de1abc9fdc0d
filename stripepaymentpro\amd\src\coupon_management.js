// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Coupon management functionality for stripepaymentpro plugin
 *
 * @module     enrol_stripepaymentpro/coupon_management
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

import ajax from 'core/ajax';
import Str from 'core/str';

let localized = {};

export const init = () => {
    Str.get_strings([
        {key: 'no_search_results', component: 'enrol_stripepaymentpro'},
        {key: 'copied', component: 'enrol_stripepaymentpro'},
        {key: 'failed', component: 'enrol_stripepaymentpro'},
        {key: 'are_you_sure_delete_coupon', component: 'enrol_stripepaymentpro'},
        {key: 'are_you_sure_delete_all_coupons', component: 'enrol_stripepaymentpro'},
        {key: 'error_deleting_coupon', component: 'enrol_stripepaymentpro'},
        {key: 'error_deleting_coupons', component: 'enrol_stripepaymentpro'},
        {key: 'success_message', component: 'enrol_stripepaymentpro'}
    ]).then(strings => {
        localized = {
            noResults: strings[0],
            copied: strings[1],
            failed: strings[2],
            confirmDelete: strings[3],
            confirmDeleteAll: strings[4],
            errorDelete: strings[5],
            errorDeleteAll: strings[6],
            success: strings[7],
        };

        const allCouponsButton = document.getElementById('all_coupon_button');
        const generateCouponsButton = document.getElementById('generate_coupons_button');
        const allCouponsSection = document.getElementById('all_coupons_section');
        const generateCouponsSection = document.getElementById('generate_coupon_section');

        const showAllCoupons = () => {
            if (allCouponsSection && generateCouponsSection) {
                allCouponsSection.style.display = 'block';
                generateCouponsSection.style.display = 'none';
            }
            if (allCouponsButton) {
                allCouponsButton.classList.remove('btn-secondary');
                allCouponsButton.classList.add('btn-primary', 'active');
            }
            if (generateCouponsButton) {
                generateCouponsButton.classList.remove('btn-primary', 'active');
                generateCouponsButton.classList.add('btn-secondary');
            }
        };

        const showGenerateCoupons = () => {
            if (allCouponsSection && generateCouponsSection) {
                allCouponsSection.style.display = 'none';
                generateCouponsSection.style.display = 'block';
            }
            if (generateCouponsButton) {
                generateCouponsButton.classList.remove('btn-secondary');
                generateCouponsButton.classList.add('btn-primary', 'active');
            }
            if (allCouponsButton) {
                allCouponsButton.classList.remove('btn-primary', 'active');
                allCouponsButton.classList.add('btn-secondary');
            }
        };

        if (allCouponsButton) {
            allCouponsButton.addEventListener('click', showAllCoupons);
        }
        if (generateCouponsButton) {
            generateCouponsButton.addEventListener('click', showGenerateCoupons);
        }

        showAllCoupons();
        initializeSearch();
        initializeCopyFunctionality();
    }).catch(error => {
        console.error('Failed to load localized strings:', error);
    });
};

const initializeSearch = () => {
    const searchInput = document.getElementById('coupon-search');
    const clearSearchButton = document.getElementById('clear-search');
    const couponsTable = document.getElementById('coupons-table');

    if (!searchInput || !couponsTable) return;

    const performSearch = () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const rows = couponsTable.querySelectorAll('tbody tr.coupon-row');
        let visibleCount = 0;

        rows.forEach(row => {
            const courseName = row.getAttribute('data-course-name')?.toLowerCase() || '';
            const couponName = row.getAttribute('data-coupon-name')?.toLowerCase() || '';
            const discountText = row.getAttribute('data-discount-text')?.toLowerCase() || '';
            const matches = !searchTerm || courseName.includes(searchTerm) || couponName.includes(searchTerm) || discountText.includes(searchTerm);
            row.style.display = matches ? '' : 'none';
            if (matches) visibleCount++;
        });

        updateNoResultsMessage(visibleCount, searchTerm);
    };

    const updateNoResultsMessage = (visibleCount, searchTerm) => {
        let noResultsRow = couponsTable.querySelector('.no-results-row');

        if (visibleCount === 0 && searchTerm !== '') {
            if (!noResultsRow) {
                noResultsRow = document.createElement('tr');
                noResultsRow.className = 'no-results-row';
                noResultsRow.innerHTML = `<td colspan="4" class="text-center text-muted py-4">
                    <i class="fa fa-search mr-2"></i>${localized.noResults}
                </td>`;
                couponsTable.querySelector('tbody').appendChild(noResultsRow);
            }
            noResultsRow.style.display = '';
        } else if (noResultsRow) {
            noResultsRow.style.display = 'none';
        }
    };

    const clearSearch = () => {
        searchInput.value = '';
        performSearch();
        searchInput.focus();
    };

    searchInput.addEventListener('input', performSearch);
    searchInput.addEventListener('keyup', e => { if (e.key === 'Escape') clearSearch(); });
    if (clearSearchButton) clearSearchButton.addEventListener('click', clearSearch);
};

const initializeCopyFunctionality = () => {
    document.addEventListener('click', event => {
        const button = event.target.closest('.copy-coupon-name');
        if (!button) return;

        const couponId = button.getAttribute('data-coupon-name');
        if (!couponId) return;

        const tempTextarea = document.createElement('textarea');
        tempTextarea.value = couponId;
        document.body.appendChild(tempTextarea);
        tempTextarea.select();
        tempTextarea.setSelectionRange(0, 99999);

        const originalText = button.innerHTML;
        try {
            document.execCommand('copy');
            button.innerHTML = `<i class="fa fa-check ml-1" style="font-size: 0.8em; color: green;"></i> ${localized.copied}`;
            button.style.color = 'green';
        } catch (err) {
            button.innerHTML = `<i class="fa fa-times ml-1" style="font-size: 0.8em; color: red;"></i> ${localized.failed}`;
            button.style.color = 'red';
        } finally {
            setTimeout(() => {
                button.innerHTML = originalText;
                button.style.color = '';
            }, 2000);
            document.body.removeChild(tempTextarea);
        }
    });
};

export const handleCouponDelete = async (courseId, couponId, button) => {
    if (!confirm(localized.confirmDelete)) return;

    try {
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        button.disabled = true;

        const request = {
            methodname: 'moodle_stripepaymentpro_deactivate_coupon',
            args: { courseid: courseId, couponid: couponId }
        };

        const result = await ajax.call([request])[0];

        if (result.success) {
            button.closest('tr')?.remove();
        } else {
            button.innerHTML = originalHTML;
            button.disabled = false;
            alert(`${localized.errorDelete}: ${result.message || 'Unknown error'}`);
        }
    } catch (error) {
        button.innerHTML = '<i class="fa fa-trash"></i>';
        button.disabled = false;
        alert(`${localized.errorDelete}: ${error.message}`);
    }
};

export const handleDeleteAllCoupons = async (courseId, button) => {
    if (!confirm(localized.confirmDeleteAll)) return;

    try {
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        button.disabled = true;

        const request = {
            methodname: 'moodle_stripepaymentpro_deactivate_all_coupons',
            args: { courseid: courseId }
        };

        const result = await ajax.call([request])[0];

        if (result.success) {
            alert(`${localized.success}: ${result.message}`);
            window.location.reload();
        } else {
            button.innerHTML = originalHTML;
            button.disabled = false;
            alert(`${localized.errorDeleteAll}: ${result.message || 'Unknown error'}`);
        }
    } catch (error) {
        button.innerHTML = '<i class="fa fa-trash-alt"></i>';
        button.disabled = false;
        alert(`${localized.errorDeleteAll}: ${error.message}`);
    }
};

window.handleCouponDelete = handleCouponDelete;
window.handleDeleteAllCoupons = handleDeleteAllCoupons;