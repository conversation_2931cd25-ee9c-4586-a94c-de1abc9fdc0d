# Stripe Payment Pro - Edit Instance Fixes Guide

## Overview
This document outlines the fixes applied to resolve issues with the Stripe Payment Pro edit instance functionality, including form display problems, price management issues, and Stripe integration improvements.

## Issues Fixed

### 1. Edit Instance Form Display Issues
**Problem**: The recurring cost field was showing Stripe price IDs instead of actual monetary values.

**Solution**: 
- Added helper methods to properly retrieve and display actual price amounts
- Improved error handling for price retrieval failures
- Enhanced form field population logic

### 2. Unnecessary Price Creation
**Problem**: New Stripe prices were being created every time the form was saved, even when no changes were made.

**Solution**:
- Implemented improved price change detection logic
- Added comprehensive comparison of all pricing parameters
- Only create new prices when actual changes are detected

### 3. Price Accumulation in Stripe
**Problem**: Old prices were never cleaned up, leading to accumulation of unused prices in Stripe.

**Solution**:
- Added price archiving functionality
- Implemented automatic cleanup of old prices when new ones are created
- Added maintenance methods for periodic cleanup

### 4. Data Consistency Issues
**Problem**: Form didn't properly remember unchanged values and price IDs could become orphaned.

**Solution**:
- Enhanced error handling with proper fallback mechanisms
- Improved data preservation during update operations
- Added validation for price ID consistency

## Files Modified

### Original Files Backed Up
- `lib.php` → `lib_original_backup.php` (original backup)

### New Fixed Version
- `lib_copy.php` (contains all fixes)

## Key Changes Made

### 1. Enhanced Edit Form (`edit_instance_form` method)
```php
// NEW: Helper methods for better form handling
private function instance_has_recurring_pricing($instance)
private function get_recurring_cost_amount($instance) 
private function get_product_display_info($instance)
```

**Improvements**:
- Properly retrieves actual price amounts instead of showing price IDs
- Better error handling for Stripe API failures
- Cleaner form field population logic
- Enhanced product information display

### 2. Improved Price Change Detection (`has_pricing_changed_improved` method)
```php
private function has_pricing_changed_improved($instance, $data)
```

**Improvements**:
- More accurate float comparison for price changes
- Better handling of recurring product state changes
- Comprehensive comparison of all pricing parameters
- Detailed debugging information for troubleshooting

### 3. Price Cleanup Functionality
```php
// NEW: Price management methods
private function archive_old_prices($instance)
private function get_active_prices_for_product($product_id)
public function cleanup_unused_prices($product_id, $keep_price_ids = [])
```

**Features**:
- Automatically archives old prices when creating new ones
- Prevents price accumulation in Stripe
- Provides maintenance methods for periodic cleanup
- Proper error handling and logging

### 4. Enhanced Update Instance Logic (`update_instance` method)
```php
public function update_instance($instance, $data)
```

**Improvements**:
- Uses improved price change detection
- Implements price archiving before creating new prices
- Better error handling with data preservation
- More robust Stripe API integration

### 5. Improved Safe Price Retrieval (`safe_price_retrieve` method)
```php
private function safe_price_retrieve($price_id)
```

**Enhancements**:
- Better error handling for different types of failures
- Improved debugging information
- Graceful handling of mode switches (test/live)
- Null safety checks

## Implementation Steps

### Step 1: Backup Original Files
```bash
# Original file backed up as:
stripepaymentpro/lib_original_backup.php
```

### Step 2: Deploy Fixed Version
1. Replace `lib.php` with the content from `lib_copy.php`
2. Or rename files:
   ```bash
   mv lib.php lib_old.php
   mv lib_copy.php lib.php
   ```

### Step 3: Test the Implementation
1. **Test Edit Instance Form**:
   - Navigate to course enrolment instances
   - Edit a Stripe Payment Pro instance
   - Verify recurring cost shows actual amount, not price ID
   - Verify product information displays correctly

2. **Test Price Change Detection**:
   - Make no changes and save → Should not create new prices
   - Change only enrolment cost → Should create new initial price only
   - Change only recurring cost → Should create new recurring price only
   - Change both costs → Should create both new prices

3. **Test Price Cleanup**:
   - Check Stripe dashboard for price accumulation
   - Verify old prices are archived when new ones are created
   - Confirm active prices are properly maintained

### Step 4: Monitor and Maintain
1. **Enable Debug Mode** (temporarily):
   ```php
   // In config.php
   $CFG->debug = DEBUG_DEVELOPER;
   ```

2. **Check Debug Logs** for:
   - Price change detection messages
   - Price archiving confirmations
   - Any error messages

3. **Periodic Maintenance**:
   - Use `cleanup_unused_prices()` method for bulk cleanup if needed
   - Monitor Stripe dashboard for price accumulation

## Testing Checklist

### Form Display Tests
- [ ] Recurring cost field shows actual amount (not price ID)
- [ ] Product ID field shows meaningful information
- [ ] Form loads without errors
- [ ] All fields populate correctly for existing instances

### Price Management Tests
- [ ] No changes + save = No new prices created
- [ ] Cost change only = New initial price only
- [ ] Recurring cost change only = New recurring price only
- [ ] Both costs change = Both new prices created
- [ ] Old prices are archived when new ones are created

### Error Handling Tests
- [ ] Form works when Stripe API is unavailable
- [ ] Graceful handling of invalid price IDs
- [ ] Proper fallback when price retrieval fails
- [ ] Data preservation during Stripe errors

### Integration Tests
- [ ] Enrolment process works correctly
- [ ] Payment processing uses correct prices
- [ ] Recurring payments function properly
- [ ] Coupon system integration maintained

## Troubleshooting

### Common Issues

1. **"Price not found" errors**:
   - Check if API key mode (test/live) has changed
   - Verify price IDs in database match Stripe
   - Use price recreation functionality if needed

2. **Form shows empty recurring cost**:
   - Check Stripe API key configuration
   - Verify price ID exists in current mode
   - Check debug logs for retrieval errors

3. **Too many prices in Stripe**:
   - Run cleanup_unused_prices() method
   - Check if price archiving is working
   - Verify old prices are being archived

### Debug Commands
```php
// Check if instance has recurring pricing
$has_recurring = $plugin->instance_has_recurring_pricing($instance);

// Get actual recurring cost amount
$amount = $plugin->get_recurring_cost_amount($instance);

// Check if prices need recreation
$needs_recreation = $plugin->prices_need_recreation($instance);

// Cleanup unused prices for a product
$archived_count = $plugin->cleanup_unused_prices($product_id, [$keep_price_1, $keep_price_2]);
```

## Benefits of the Fixes

1. **Better User Experience**: Form shows actual values instead of cryptic IDs
2. **Reduced Stripe Costs**: Fewer unnecessary API calls and price objects
3. **Improved Reliability**: Better error handling and data consistency
4. **Easier Maintenance**: Clear debugging and cleanup tools
5. **Enhanced Performance**: Optimized price change detection

## Future Enhancements

1. **Automated Price Cleanup**: Scheduled task to clean up old prices
2. **Price History Tracking**: Log of price changes for audit purposes
3. **Bulk Price Management**: Tools for managing multiple instances
4. **Enhanced Validation**: More comprehensive form validation
5. **API Rate Limiting**: Better handling of Stripe API limits

## Code Comparison: Key Method Changes

### Before vs After: edit_instance_form method

**BEFORE (Problem)**:
```php
// Get recurring cost amount safely
$recurring_cost_amount = '';
if (isset($instance->customtext4) && !empty($instance->customtext4) && $instance->customtext4 !== '0') {
    try {
        $secretkey = $plugin->get_current_secret_key();
        if (!empty($secretkey)) {
            \Stripe\Stripe::setApiKey($secretkey);
            $recurring_price = $this->safe_price_retrieve($instance->customtext4);
            if ($recurring_price && isset($recurring_price->unit_amount)) {
                $currency = !empty($instance->currency) ? $instance->currency : 'USD';
                $recurring_cost_amount = $recurring_price->unit_amount / $this->get_fractional_unit_amount($currency);
            }
        }
    } catch (Exception $e) {
        // If we can't retrieve the price, leave the field empty
        // Silently fail to avoid form errors
    }
}
$mform->setDefault('customtext4', $recurring_cost_amount);
```

**AFTER (Fixed)**:
```php
// Determine if this instance has recurring pricing
$has_recurring = $this->instance_has_recurring_pricing($instance);
$mform->setDefault('recurringproduct', $has_recurring ? 1 : 0);

// Get and set the actual recurring cost amount (not the price ID)
$recurring_cost_amount = $this->get_recurring_cost_amount($instance);
$mform->setDefault('customtext4', $recurring_cost_amount);

// Helper methods:
private function instance_has_recurring_pricing($instance) {
    return isset($instance->customtext4) &&
           !empty($instance->customtext4) &&
           $instance->customtext4 !== '0' &&
           $instance->customtext4 !== 0;
}

private function get_recurring_cost_amount($instance) {
    if (!$this->instance_has_recurring_pricing($instance)) {
        return '';
    }
    try {
        $secretkey = $plugin->get_current_secret_key();
        if (empty($secretkey)) {
            return '';
        }
        \Stripe\Stripe::setApiKey($secretkey);
        $recurring_price = $this->safe_price_retrieve($instance->customtext4);

        if ($recurring_price && isset($recurring_price->unit_amount)) {
            $currency = !empty($instance->currency) ? $instance->currency : 'USD';
            return $recurring_price->unit_amount / $this->get_fractional_unit_amount($currency);
        }
    } catch (Exception $e) {
        debugging('Error retrieving recurring price: ' . $e->getMessage(), DEBUG_DEVELOPER);
    }
    return '';
}
```

### Before vs After: update_instance method

**BEFORE (Problem)**:
```php
// Check if pricing data has actually changed
$pricing_changed = $this->has_pricing_changed($instance, $data);

// Additional safety check - if no changes detected, don't create new prices
if (!$pricing_changed) {
    // Ensure we keep existing price IDs
    $data->customtext3 = $instance->customtext3;
    $data->customtext4 = $instance->customtext4;
    $data->customtext1 = $instance->customtext1;
}

if ($pricing_changed) {
    // Update existing product
    $product = \Stripe\Product::retrieve($instance->customtext2);

    // Create new initial price only if changed
    $initial_amount = $data->cost * $this->get_fractional_unit_amount($data->currency);
    $initial_price = Price::create([
        'unit_amount' => $initial_amount,
        'currency' => $data->currency,
        'product' => $product->id,
    ]);
    // ... rest of price creation without cleanup
}
```

**AFTER (Fixed)**:
```php
// Check if pricing data has actually changed using improved detection
$pricing_changed = $this->has_pricing_changed_improved($instance, $data);

if ($pricing_changed) {
    // Get existing product
    $product = \Stripe\Product::retrieve($instance->customtext2);

    // Archive old prices before creating new ones
    $this->archive_old_prices($instance);

    // Create new initial price
    $initial_amount = $data->cost * $this->get_fractional_unit_amount($data->currency);
    $initial_price = Price::create([
        'unit_amount' => $initial_amount,
        'currency' => $data->currency,
        'product' => $product->id,
    ]);
    // ... rest with proper cleanup and error handling
} else {
    // No changes detected, preserve existing price IDs
    $data->customtext3 = $instance->customtext3;
    $data->customtext4 = $instance->customtext4;
    $data->customtext1 = $instance->customtext1;
}
```

### New Methods Added

**Price Cleanup Methods**:
```php
private function archive_old_prices($instance) {
    try {
        // Archive old initial price if it exists
        if (!empty($instance->customtext3) && $instance->customtext3 !== '0') {
            $old_initial_price = $this->safe_price_retrieve($instance->customtext3);
            if ($old_initial_price && $old_initial_price->active) {
                Price::update($instance->customtext3, ['active' => false]);
                debugging('Archived old initial price: ' . $instance->customtext3, DEBUG_DEVELOPER);
            }
        }
        // Similar for recurring price...
    } catch (Exception $e) {
        debugging('Error archiving old prices: ' . $e->getMessage(), DEBUG_DEVELOPER);
    }
}

public function cleanup_unused_prices($product_id, $keep_price_ids = []) {
    $archived_count = 0;
    try {
        $active_prices = $this->get_active_prices_for_product($product_id);
        foreach ($active_prices as $price) {
            if (in_array($price->id, $keep_price_ids)) {
                continue;
            }
            Price::update($price->id, ['active' => false]);
            $archived_count++;
            debugging('Archived unused price: ' . $price->id, DEBUG_DEVELOPER);
        }
    } catch (Exception $e) {
        debugging('Error during price cleanup: ' . $e->getMessage(), DEBUG_DEVELOPER);
    }
    return $archived_count;
}
```

**Improved Price Change Detection**:
```php
private function has_pricing_changed_improved($instance, $data) {
    // Check if initial cost has changed (with proper float comparison)
    if (abs((float)$instance->cost - (float)$data->cost) > 0.001) {
        debugging('Cost changed: ' . $instance->cost . ' -> ' . $data->cost, DEBUG_DEVELOPER);
        return true;
    }

    // Determine current and new recurring states more accurately
    $old_recurring = $this->instance_has_recurring_pricing($instance);
    $new_recurring = !empty($data->recurringproduct) &&
                    isset($data->customtext4) &&
                    $data->customtext4 !== '' &&
                    $data->customtext4 !== '0' &&
                    (float)$data->customtext4 > 0;

    if ($old_recurring !== $new_recurring) {
        debugging('Recurring state changed: ' . ($old_recurring ? 'true' : 'false') . ' -> ' . ($new_recurring ? 'true' : 'false'), DEBUG_DEVELOPER);
        return true;
    }

    // If both have recurring pricing, check if recurring details changed
    if ($new_recurring && $old_recurring) {
        $current_recurring_price = $this->safe_price_retrieve($instance->customtext4);
        if (!$current_recurring_price) {
            debugging('Could not retrieve current recurring price, assuming changed', DEBUG_DEVELOPER);
            return true;
        }

        $current_recurring_amount = $current_recurring_price->unit_amount / $this->get_fractional_unit_amount($instance->currency);
        if (abs($current_recurring_amount - (float)$data->customtext4) > 0.001) {
            debugging('Recurring amount changed: ' . $current_recurring_amount . ' -> ' . $data->customtext4, DEBUG_DEVELOPER);
            return true;
        }
        // ... additional checks for interval and interval_count
    }

    debugging('No pricing changes detected', DEBUG_DEVELOPER);
    return false;
}
```

---

**Note**: Always test changes in a development environment before deploying to production. Keep backups of original files and database before making changes.
