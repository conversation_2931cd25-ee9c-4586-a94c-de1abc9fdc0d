# Stripe Payment Plugin - Workflow Fixes Summary

## Issues Fixed

### 1. Zip File Not Saved in Release Folder
**Problem**: The GitHub workflow was creating zip files but not saving them in the `release/` folder.

**Solution**:
- Updated workflow to save zip files only in `release/` folder (removed dist folder)
- Added a dedicated release job that runs on tags and creates GitHub releases
- Modified composer scripts to support both Windows (PowerShell) and Unix (zip command) environments

### 2. Missing Moodle Plugin CI Checks
**Problem**: The workflow was missing several important moodle-plugin-ci checks.

**Solution**: Added all standard moodle-plugin-ci checks:
- `moodle-plugin-ci phpcpd` - PHP Copy/Paste Detector
- `moodle-plugin-ci phpcs` - Moodle Code Checker (PHP CodeSniffer)
- `moodle-plugin-ci mustache` - Mustache template validation
- `moodle-plugin-ci grunt` - Grunt build validation

### 3. Missing Local Development Tools
**Problem**: No way to run the same quality checks locally that are performed in CI.

**Solution**: Created comprehensive local development scripts:
- `run-local-checks.sh` - Linux/macOS bash script
- `run-local-checks.bat` - Windows batch script
- `run-local-checks.ps1` - Windows PowerShell script
- `LOCAL_DEVELOPMENT.md` - Comprehensive development guide

## Files Modified

### Workflow Files
- `.github/workflows/moodle-ci.yml` - Updated zip handling and added release job

### Composer Configuration
- `composer.json` - Added new scripts for cross-platform zip creation and quality checks

### Local Development Scripts
- `run-local-checks.sh` - Bash script for Unix systems
- `run-local-checks.bat` - Batch script for Windows
- `run-local-checks.ps1` - PowerShell script for Windows
- `LOCAL_DEVELOPMENT.md` - Development documentation

## Key Improvements

### 1. Cross-Platform Zip Creation
```json
{
  "zip": ["@build", "@zip-create"],
  "zip-create": [
    "powershell -Command \"if (!(Test-Path 'release')) { New-Item -ItemType Directory -Path 'release' }\"",
    "powershell -Command \"Compress-Archive -Path . -DestinationPath release/stripepayment.zip -Force\""
  ],
  "zip-unix": [
    "@build",
    "mkdir -p release",
    "zip -r release/stripepayment.zip . --exclude @.zipignore"
  ]
}
```

### 2. Complete Moodle Plugin CI Integration
Added all standard moodle-plugin-ci checks:
- PHP Lint (`moodle-plugin-ci phplint`)
- PHP Copy/Paste Detector (`moodle-plugin-ci phpcpd`)
- PHP Mess Detector (`moodle-plugin-ci phpmd`)
- Moodle Code Checker (`moodle-plugin-ci phpcs`)
- PHP Code Beautifier and Fixer (`moodle-plugin-ci phpcbf`)
- Plugin Validation (`moodle-plugin-ci validate`)
- Mustache Template Validation (`moodle-plugin-ci mustache`)
- Grunt Build Validation (`moodle-plugin-ci grunt`)
- Upgrade Savepoints Check (`moodle-plugin-ci savepoints`)
- PHPUnit Tests (`moodle-plugin-ci phpunit`)
- Behat Tests (`moodle-plugin-ci behat`)

### 3. Automated Release Process
- Release job only runs on git tags
- Automatically creates GitHub releases with zip attachments
- Zip files saved only in `release/` folder (no more dist folder)
- Maintains backward compatibility with existing CI process

### 4. Local Quality Checks
The local scripts run the same checks as CI:
- PHP Lint
- PHP CodeSniffer (PHPCS)
- PHP Code Beautifier and Fixer (PHPCBF)
- PHP Mess Detector (PHPMD)
- Plugin structure validation
- Version.php format checking
- Zip package creation

## How to Use

### Quick Local Testing
```bash
# Windows
run-local-checks.bat

# Linux/macOS  
./run-local-checks.sh

# PowerShell (any platform)
powershell -ExecutionPolicy Bypass -File run-local-checks.ps1
```

### Manual Quality Checks
```bash
# Install dependencies
composer install
npm install

# Build assets
npm run build

# Run individual checks
composer run-script phpcs    # Code standards
composer run-script phpcf    # Auto-fix standards
composer run-script phpmd    # Mess detection

# Create release package
composer run-script zip      # Windows (PowerShell)
composer run-script zip-unix # Linux/macOS
```

### Full Moodle Plugin CI
```bash
# Install moodle-plugin-ci
composer create-project -n --no-dev --prefer-dist moodlehq/moodle-plugin-ci ci ^4
export PATH=$PATH:$(pwd)/ci/bin:$(pwd)/ci/vendor/bin

# Setup and run all checks
moodle-plugin-ci install --plugin . --db-host=127.0.0.1
moodle-plugin-ci phplint .
moodle-plugin-ci phpcpd .
moodle-plugin-ci phpmd .
moodle-plugin-ci phpcs .
moodle-plugin-ci phpcbf .
moodle-plugin-ci validate .
moodle-plugin-ci mustache .
moodle-plugin-ci grunt .
moodle-plugin-ci savepoints .
moodle-plugin-ci phpunit .
moodle-plugin-ci behat .
```

## Release Process

1. **Development**: Use local scripts to test changes
2. **Commit**: Push changes to repository
3. **Tag**: Create and push a version tag
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```
4. **Automatic**: GitHub Actions creates release with zip file in `release/` folder

## Verification

To verify the fixes work:

1. **Local zip creation**: Run `composer run-script zip` - should create `release/stripepayment.zip`
2. **Local checks**: Run the appropriate local check script for your platform
3. **CI workflow**: Push changes and verify workflow completes successfully
4. **Release**: Create a tag and verify the release job creates a GitHub release

## Notes

- The `release/` folder is now the only location for zip files (dist folder removed)
- All standard moodle-plugin-ci checks are now included in the workflow
- All quality checks can be run locally before pushing
- The workflow is backward compatible with existing processes
- Cross-platform support for Windows, Linux, and macOS development environments
- Complete parity between local checks and CI workflow
