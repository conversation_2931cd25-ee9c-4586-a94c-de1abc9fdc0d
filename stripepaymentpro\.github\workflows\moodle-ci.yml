name: Moodle Plugin CI

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-24.04

    services:
      mariadb:
        image: mariadb:10.11.7
        env:
          MYSQL_ROOT_PASSWORD: ''
          MYSQL_ALLOW_EMPTY_PASSWORD: "true"
          MYSQL_CHARACTER_SET_SERVER: "utf8mb4"
          MYSQL_COLLATION_SERVER: "utf8mb4_unicode_ci"
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping --silent" --health-interval=10s --health-timeout=5s --health-retries=3

    strategy:
      fail-fast: false
      matrix:
        php: ['8.4']
        moodle-branch: ['MOODLE_500_STABLE']
        database: [mariadb]

    steps:
      - name: Checkout Plugin
        uses: actions/checkout@v4
        with:
          path: plugin

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: mbstring, intl, soap, curl, gd, xml, json, zip, pdo, pdo_mysql,
          ini-values: max_input_vars=5000
          coverage: none

      - name: Setup Node.js (for Grunt)
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install Moodle Node dependencies
        run: npm install
        working-directory: plugin

      - name: Build grunt
        run: npm run build
        working-directory: plugin

      - name: Install Plugin Composer Dependencies
        run: composer install --no-dev --prefer-dist --no-progress
        working-directory: plugin

      - name: Prepare Release Folder
        run: mkdir -p release

      - name: Build Zip & Clean Plugin Directory
        run: |
          composer run-script zip-unix
        working-directory: plugin

      - name: Copy Zip to release/
        run: |
          cp plugin/release/stripepaymentpro.zip ${{ github.workspace }}/release/

      - name: Upload plugin zip
        uses: actions/upload-artifact@v4
        with:
          name: stripepaymentpro
          path: release/stripepaymentpro.zip
          retention-days: 14


      - name: Clean Extra Files from Plugin
        run: |
          rm -rf plugin/.git plugin/.github plugin/node_modules plugin/tests plugin/phpunit.xml
          rm -f plugin/composer.* plugin/package*.json plugin/webpack.config.js plugin/.zipignore

      - name: Install moodle-plugin-ci tooling
        run: |
          composer create-project -n --no-dev --prefer-dist moodlehq/moodle-plugin-ci ci ^4
          echo "$(cd ci/bin; pwd)" >> $GITHUB_PATH
          echo "$(cd ci/vendor/bin; pwd)" >> $GITHUB_PATH
          sudo locale-gen en_AU.UTF-8
        env:
          COMPOSER_NO_INTERACTION: 1

      - name: Install moodle-plugin-ci
        run: moodle-plugin-ci install --plugin ./plugin --db-host=127.0.0.1
        env:
          DB: ${{ matrix.database }}
          MOODLE_BRANCH: ${{ matrix.moodle-branch }}

      - name: PHP Lint
        if: ${{ !cancelled() }}
        run: moodle-plugin-ci phplint plugin

      - name: PHP Mess Detector
        if: ${{ !cancelled() }}
        continue-on-error: true
        run: moodle-plugin-ci phpmd plugin

      - name: PHP Code Beautifier and Fixer
        if: ${{ !cancelled() }}
        run: moodle-plugin-ci phpcbf plugin

      - name: Validate Plugin
        if: ${{ !cancelled() }}
        run: moodle-plugin-ci validate plugin

      - name: Check Upgrade Savepoints
        if: ${{ !cancelled() }}
        run: moodle-plugin-ci savepoints plugin

      - name: PHPUnit Tests
        if: ${{ !cancelled() }}
        run: moodle-plugin-ci phpunit --fail-on-warning plugin

      - name: Run Behat Tests
        id: behat
        if: ${{ !cancelled() }}
        run: moodle-plugin-ci behat --profile chrome --scss-deprecations plugin


      - name: Upload Behat Faildump (if failed)
        if: ${{ failure() && steps.behat.outcome == 'failure' }}
        uses: actions/upload-artifact@v4
        with:
          name: Behat Faildump (${{ matrix.php }}, ${{ matrix.database }})
          path: ${{ github.workspace }}/moodledata/behat_dump
          retention-days: 7
          if-no-files-found: ignore
      
      - name: Final Cleanup - Remove Vendor
        if: ${{ always() }}
        run: rm -rf plugin/vendor

      - name: Mark cancelled jobs as failed
        if: ${{ cancelled() }}
        run: exit 1
  release:
    runs-on: ubuntu-24.04
    needs: test
    if: startsWith(github.ref, 'refs/tags/')

    steps:
      - name: Checkout Plugin
        uses: actions/checkout@v4
        with:
          path: plugin

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          extensions: mbstring, intl, soap, curl, gd, xml, json, zip, pdo, pdo_mysql,
          ini-values: max_input_vars=5000
          coverage: none

      - name: Setup Node.js (for Grunt)
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install Moodle Node dependencies
        run: npm install
        working-directory: plugin

      - name: Build grunt
        run: npm run build
        working-directory: plugin

      - name: Install Plugin Composer Dependencies
        run: composer install --no-dev --prefer-dist --no-progress
        working-directory: plugin

      - name: Prepare Release Folder
        run: mkdir -p release

      - name: Build Release Zip
        run: |
          composer run-script zip-unix
          if [ ! -f release/stripepaymentpro.zip ]; then
            echo "Error: stripepaymentpro.zip was not created"
            exit 1
          fi
        working-directory: plugin

      - name: Move Zip to release/
        run: |
          if [ -f plugin/release/stripepaymentpro.zip ]; then
            mv plugin/release/stripepaymentpro.zip ${{ github.workspace }}/release/
          else
            echo "Error: stripepaymentpro.zip not found"
            exit 1
          fi

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          files: release/stripepaymentpro.zip
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
